const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createAdmin() {
  try {
    console.log('🔧 Creating admin users...');

    // Create Super Admin first
    const superAdminEmail = '<EMAIL>';
    const existingSuperAdmin = await prisma.admin.findUnique({
      where: { email: superAdminEmail }
    });

    let superAdmin;
    if (!existingSuperAdmin) {
      const hashedPassword = await bcrypt.hash('superadmin123', 12);
      superAdmin = await prisma.admin.create({
        data: {
          email: superAdminEmail,
          password: hashedPassword,
          name: 'Super Administrator',
          role: 'super_admin',
          isActive: true
        }
      });
      console.log('✅ Super Admin created successfully!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: superadmin123');
      console.log('👤 Name:', superAdmin.name);
      console.log('🆔 ID:', superAdmin.id);
    } else {
      superAdmin = existingSuperAdmin;
      console.log('ℹ️ Super Admin already exists:', superAdmin.email);
    }

    // Create Regular Admin
    const adminEmail = '<EMAIL>';
    const existingAdmin = await prisma.admin.findUnique({
      where: { email: adminEmail }
    });

    if (!existingAdmin) {
      const hashedPassword = await bcrypt.hash('admin123', 12);
      const admin = await prisma.admin.create({
        data: {
          email: adminEmail,
          password: hashedPassword,
          name: 'Admin User',
          role: 'admin',
          isActive: true,
          createdBy: superAdmin.id
        }
      });
      console.log('✅ Regular Admin created successfully!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: admin123');
      console.log('👤 Name:', admin.name);
      console.log('🆔 ID:', admin.id);
    } else {
      console.log('ℹ️ Regular Admin already exists:', existingAdmin.email);
    }

    // Create system settings if not exists
    const systemSettings = await prisma.systemSettings.findFirst();
    if (!systemSettings) {
      await prisma.systemSettings.create({
        data: {
          defaultLanguage: 'si' // Default to Sinhala as per user preference
        }
      });
      console.log('✅ System settings created with Sinhala as default language');
    }

    console.log('\n🎉 Admin setup completed successfully!');

  } catch (error) {
    console.error('❌ Error creating admin users:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

createAdmin();
