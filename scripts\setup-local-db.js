#!/usr/bin/env node

/**
 * Local Database Setup Script for AstroConnect
 * This script helps set up a local PostgreSQL database for development
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 AstroConnect Local Database Setup\n');

// Check if PostgreSQL is installed
function checkPostgreSQL() {
  try {
    console.log('1️⃣ Checking PostgreSQL installation...');
    execSync('psql --version', { stdio: 'pipe' });
    console.log('✅ PostgreSQL is installed');
    return true;
  } catch (error) {
    console.log('❌ PostgreSQL is not installed or not in PATH');
    console.log('\n📥 Please install PostgreSQL:');
    console.log('   Windows: https://www.postgresql.org/download/windows/');
    console.log('   macOS: brew install postgresql');
    console.log('   Linux: sudo apt-get install postgresql postgresql-contrib');
    return false;
  }
}

// Check if local database exists
function checkDatabase() {
  try {
    console.log('\n2️⃣ Checking if local database exists...');
    // Try different common PostgreSQL configurations
    const commands = [
      'psql -U postgres -d astroconnect_dev -c "SELECT 1;" 2>/dev/null',
      'psql -h localhost -U postgres -d astroconnect_dev -c "SELECT 1;" 2>/dev/null'
    ];

    for (const cmd of commands) {
      try {
        execSync(cmd, { stdio: 'pipe' });
        console.log('✅ Database "astroconnect_dev" exists');
        return true;
      } catch (e) {
        // Continue to next command
      }
    }

    console.log('❌ Database "astroconnect_dev" does not exist');
    return false;
  } catch (error) {
    console.log('❌ Database "astroconnect_dev" does not exist');
    return false;
  }
}

// Create local database
function createDatabase() {
  try {
    console.log('\n3️⃣ Creating local database...');
    console.log('📝 You may be prompted for the PostgreSQL password');
    console.log('💡 Common PostgreSQL passwords: postgres, password, or empty');

    // Try different common PostgreSQL configurations
    const commands = [
      'psql -U postgres -c "CREATE DATABASE astroconnect_dev;"',
      'psql -h localhost -U postgres -c "CREATE DATABASE astroconnect_dev;"'
    ];

    for (const cmd of commands) {
      try {
        execSync(cmd, { stdio: 'inherit' });
        console.log('✅ Database "astroconnect_dev" created successfully');
        return true;
      } catch (e) {
        console.log(`⚠️  Command failed: ${cmd}`);
      }
    }

    console.log('❌ Failed to create database. Please ensure:');
    console.log('   - PostgreSQL service is running');
    console.log('   - You have the correct password for postgres user');
    console.log('   - The database name "astroconnect_dev" is not already taken');
    console.log('\n🔧 Manual database creation:');
    console.log('   psql -U postgres');
    console.log('   CREATE DATABASE astroconnect_dev;');
    console.log('   \\q');
    return false;
  } catch (error) {
    console.log('❌ Failed to create database:', error.message);
    return false;
  }
}

// Run Prisma migrations
function runPrismaMigrations() {
  try {
    console.log('\n4️⃣ Running Prisma migrations...');
    
    // Generate Prisma client
    console.log('📦 Generating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });
    
    // Push database schema
    console.log('🔄 Pushing database schema...');
    execSync('npx prisma db push', { stdio: 'inherit' });
    
    console.log('✅ Prisma setup completed');
    return true;
  } catch (error) {
    console.log('❌ Failed to run Prisma migrations:', error.message);
    return false;
  }
}

// Check environment configuration
function checkEnvironment() {
  console.log('\n5️⃣ Checking environment configuration...');
  
  const envLocalPath = path.join(process.cwd(), '.env.local');
  const envPath = path.join(process.cwd(), '.env');
  
  if (fs.existsSync(envLocalPath)) {
    const envContent = fs.readFileSync(envLocalPath, 'utf8');
    if (envContent.includes('localhost:5432/astroconnect_dev') && envContent.includes('NODE_ENV=development')) {
      console.log('✅ .env.local is configured for local development');
    } else {
      console.log('⚠️  .env.local exists but may not be configured for local development');
    }
  } else {
    console.log('⚠️  .env.local file not found');
  }

  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    if (envContent.includes('localhost:5432/astroconnect_dev')) {
      console.log('✅ .env is configured for local development');
    } else {
      console.log('⚠️  .env exists but may not be configured for local development');
    }
  } else {
    console.log('⚠️  .env file not found');
  }
}

// Main setup function
async function main() {
  try {
    // Step 1: Check PostgreSQL
    if (!checkPostgreSQL()) {
      process.exit(1);
    }
    
    // Step 2: Check if database exists
    const dbExists = checkDatabase();
    
    // Step 3: Create database if it doesn't exist
    if (!dbExists) {
      if (!createDatabase()) {
        process.exit(1);
      }
    }
    
    // Step 4: Run Prisma migrations
    if (!runPrismaMigrations()) {
      process.exit(1);
    }
    
    // Step 5: Check environment
    checkEnvironment();
    
    console.log('\n🎉 Local database setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Make sure your .env.local file has the correct local database URL');
    console.log('   2. Run: npm run dev');
    console.log('   3. Visit: http://localhost:3000');
    console.log('\n💡 To populate with sample data, run:');
    console.log('   node scripts/populate-sample-data.js');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup
main();
