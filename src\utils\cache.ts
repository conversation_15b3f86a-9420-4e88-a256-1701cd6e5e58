/**
 * Cache Management Utilities
 * Provides functions to manage browser and service worker caches
 */

export class CacheManager {
  private static instance: CacheManager;
  
  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  /**
   * Clear all browser caches
   */
  async clearAllCaches(): Promise<void> {
    try {
      // Clear service worker caches
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
        console.log('✅ Service worker caches cleared');
      }

      // Clear localStorage
      if (typeof Storage !== 'undefined') {
        localStorage.clear();
        console.log('✅ localStorage cleared');
      }

      // Clear sessionStorage
      if (typeof Storage !== 'undefined') {
        sessionStorage.clear();
        console.log('✅ sessionStorage cleared');
      }

      console.log('🧹 All caches cleared successfully');
    } catch (error) {
      console.error('❌ Error clearing caches:', error);
      throw error;
    }
  }

  /**
   * Clear specific cache by name
   */
  async clearCache(cacheName: string): Promise<void> {
    try {
      if ('caches' in window) {
        const deleted = await caches.delete(cacheName);
        if (deleted) {
          console.log(`✅ Cache '${cacheName}' cleared`);
        } else {
          console.log(`⚠️ Cache '${cacheName}' not found`);
        }
      }
    } catch (error) {
      console.error(`❌ Error clearing cache '${cacheName}':`, error);
      throw error;
    }
  }

  /**
   * Clear API response caches
   */
  async clearApiCaches(): Promise<void> {
    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        for (const cacheName of cacheNames) {
          const cache = await caches.open(cacheName);
          const requests = await cache.keys();
          
          for (const request of requests) {
            if (request.url.includes('/api/')) {
              await cache.delete(request);
            }
          }
        }
        console.log('✅ API caches cleared');
      }
    } catch (error) {
      console.error('❌ Error clearing API caches:', error);
      throw error;
    }
  }

  /**
   * Clear admin panel caches
   */
  async clearAdminCaches(): Promise<void> {
    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        for (const cacheName of cacheNames) {
          const cache = await caches.open(cacheName);
          const requests = await cache.keys();
          
          for (const request of requests) {
            if (request.url.includes('/admin') || request.url.includes('/api/admin/')) {
              await cache.delete(request);
            }
          }
        }
        console.log('✅ Admin caches cleared');
      }

      // Clear admin-specific localStorage items
      const adminKeys = Object.keys(localStorage).filter(key => 
        key.includes('admin') || key.includes('token')
      );
      adminKeys.forEach(key => localStorage.removeItem(key));
      
      console.log('✅ Admin localStorage cleared');
    } catch (error) {
      console.error('❌ Error clearing admin caches:', error);
      throw error;
    }
  }

  /**
   * Force reload the page with cache bypass
   */
  forceReload(): void {
    try {
      // Force reload with cache bypass
      window.location.reload();
    } catch (error) {
      console.error('❌ Error forcing reload:', error);
    }
  }

  /**
   * Get cache information
   */
  async getCacheInfo(): Promise<{
    cacheNames: string[];
    totalSize: number;
    apiCacheCount: number;
    adminCacheCount: number;
  }> {
    try {
      if (!('caches' in window)) {
        return {
          cacheNames: [],
          totalSize: 0,
          apiCacheCount: 0,
          adminCacheCount: 0
        };
      }

      const cacheNames = await caches.keys();
      let totalSize = 0;
      let apiCacheCount = 0;
      let adminCacheCount = 0;

      for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const requests = await cache.keys();
        
        for (const request of requests) {
          if (request.url.includes('/api/')) {
            apiCacheCount++;
          }
          if (request.url.includes('/admin')) {
            adminCacheCount++;
          }
        }
        
        totalSize += requests.length;
      }

      return {
        cacheNames,
        totalSize,
        apiCacheCount,
        adminCacheCount
      };
    } catch (error) {
      console.error('❌ Error getting cache info:', error);
      return {
        cacheNames: [],
        totalSize: 0,
        apiCacheCount: 0,
        adminCacheCount: 0
      };
    }
  }

  /**
   * Add cache-busting parameter to URL
   */
  addCacheBuster(url: string): string {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}_cb=${Date.now()}`;
  }

  /**
   * Check if service worker is available and active
   */
  isServiceWorkerActive(): boolean {
    return 'serviceWorker' in navigator && 
           navigator.serviceWorker.controller !== null;
  }

  /**
   * Update service worker
   */
  async updateServiceWorker(): Promise<void> {
    try {
      if ('serviceWorker' in navigator) {
        const registration = await navigator.serviceWorker.getRegistration();
        if (registration) {
          await registration.update();
          console.log('✅ Service worker updated');
        }
      }
    } catch (error) {
      console.error('❌ Error updating service worker:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const cacheManager = CacheManager.getInstance();

// Utility functions for common cache operations
export const clearAllCaches = () => cacheManager.clearAllCaches();
export const clearApiCaches = () => cacheManager.clearApiCaches();
export const clearAdminCaches = () => cacheManager.clearAdminCaches();
export const forceReload = () => cacheManager.forceReload();
export const addCacheBuster = (url: string) => cacheManager.addCacheBuster(url);
