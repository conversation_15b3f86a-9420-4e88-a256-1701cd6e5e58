#!/usr/bin/env node

/**
 * Test Local Database Connection
 * This script tests the connection to the local PostgreSQL database
 */

const { PrismaClient } = require('@prisma/client');

async function testConnection() {
  console.log('🔍 Testing local database connection...\n');
  
  const prisma = new PrismaClient();
  
  try {
    // Test basic connection
    console.log('1️⃣ Testing database connection...');
    await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Database connection successful');
    
    // Test if tables exist
    console.log('\n2️⃣ Checking database schema...');
    try {
      const result = await prisma.$queryRaw`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name;
      `;
      
      if (result.length > 0) {
        console.log('✅ Database schema exists');
        console.log(`📊 Found ${result.length} tables:`);
        result.forEach(table => {
          console.log(`   - ${table.table_name}`);
        });
      } else {
        console.log('⚠️  Database schema not found. Run: npx prisma db push');
      }
    } catch (error) {
      console.log('⚠️  Database schema check failed. Run: npx prisma db push');
    }
    
    console.log('\n✅ Local database connection test passed!');
    console.log('\n📋 Next steps:');
    console.log('   1. If schema is missing, run: npx prisma db push');
    console.log('   2. To add sample data, run: node scripts/populate-sample-data.js');
    console.log('   3. Start development server: npm run dev');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Make sure PostgreSQL is running');
    console.log('   2. Check your database URL in .env.local');
    console.log('   3. Verify database credentials');
    console.log('   4. Create database: psql -U postgres -c "CREATE DATABASE astroconnect_dev;"');
    console.log('\n📝 Current DATABASE_URL from environment:');
    console.log(`   ${process.env.DATABASE_URL || 'Not set'}`);
    
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

testConnection();
