// Load environment variables
require('dotenv').config({ path: '.env.local' });

const fs = require('fs');
const path = require('path');
const { createCanvas, loadImage } = require('canvas');
const jsQR = require('jsqr');

async function testQRDecode(imagePath) {
  try {
    console.log('🔍 Testing QR code decoding...');
    console.log('📁 Image path:', imagePath);

    // Check if file exists
    if (!fs.existsSync(imagePath)) {
      throw new Error(`Image file not found: ${imagePath}`);
    }

    // Load image
    console.log('📖 Loading image...');
    const image = await loadImage(imagePath);
    console.log(`✅ Image loaded: ${image.width}x${image.height}`);

    // Create canvas
    const canvas = createCanvas(image.width, image.height);
    const ctx = canvas.getContext('2d');

    // Draw image to canvas
    ctx.drawImage(image, 0, 0);

    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    console.log(`📊 Image data: ${imageData.width}x${imageData.height}, ${imageData.data.length} bytes`);

    // Try to decode QR code
    console.log('🔍 Scanning for QR code...');
    const qrResult = jsQR(imageData.data, imageData.width, imageData.height);

    if (qrResult) {
      console.log('✅ QR Code found!');
      console.log('📄 Data:', qrResult.data);
      console.log('📍 Location:', qrResult.location);
      
      // Try to extract token from URL
      const urlMatch = qrResult.data.match(/\/qr\/([a-f0-9-]+)/i);
      if (urlMatch) {
        console.log('🎯 Extracted token:', urlMatch[1]);
      }
      
      return qrResult.data;
    } else {
      console.log('❌ No QR code found in image');
      
      // Try with enhanced contrast
      console.log('🔄 Trying with enhanced contrast...');
      const data = imageData.data;
      for (let i = 0; i < data.length; i += 4) {
        const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
        const enhanced = gray > 128 ? 255 : 0;
        data[i] = enhanced;
        data[i + 1] = enhanced;
        data[i + 2] = enhanced;
      }
      
      const enhancedResult = jsQR(imageData.data, imageData.width, imageData.height);
      if (enhancedResult) {
        console.log('✅ QR Code found with enhancement!');
        console.log('📄 Data:', enhancedResult.data);
        return enhancedResult.data;
      } else {
        console.log('❌ No QR code found even with enhancement');
        return null;
      }
    }

  } catch (error) {
    console.error('❌ Error testing QR decode:', error);
    return null;
  }
}

// Test with command line argument or default test image
const imagePath = process.argv[2] || 'test-qr.png';
testQRDecode(imagePath)
  .then((result) => {
    if (result) {
      console.log('\n🎉 QR decode test successful!');
      process.exit(0);
    } else {
      console.log('\n❌ QR decode test failed');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
