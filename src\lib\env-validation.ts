/**
 * Environment Variables Validation
 * Ensures all required environment variables are present and valid
 */

interface EnvConfig {
  // Database
  DATABASE_URL: string;
  
  // Authentication
  JWT_SECRET: string;
  
  // External APIs
  GEMINI_API_KEY: string;
  
  // Application
  NEXT_PUBLIC_APP_URL: string;
  NODE_ENV: 'development' | 'production' | 'test';
}

/**
 * Validate and return typed environment variables
 */
export function validateEnv(): EnvConfig {
  const requiredVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'GEMINI_API_KEY',
    'NEXT_PUBLIC_APP_URL'
  ];

  const missingVars: string[] = [];
  const invalidVars: string[] = [];

  // Check for missing variables
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  }

  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  // Validate specific formats
  const databaseUrl = process.env.DATABASE_URL!;
  if (!databaseUrl.startsWith('postgres://') && !databaseUrl.startsWith('postgresql://')) {
    invalidVars.push('DATABASE_URL (must be a valid PostgreSQL connection string)');
  }

  const jwtSecret = process.env.JWT_SECRET!;
  if (jwtSecret.length < 32) {
    invalidVars.push('JWT_SECRET (must be at least 32 characters long)');
  }

  const appUrl = process.env.NEXT_PUBLIC_APP_URL!;
  if (!appUrl.startsWith('http://') && !appUrl.startsWith('https://')) {
    invalidVars.push('NEXT_PUBLIC_APP_URL (must be a valid URL)');
  }

  if (invalidVars.length > 0) {
    throw new Error(`Invalid environment variables: ${invalidVars.join(', ')}`);
  }

  return {
    DATABASE_URL: databaseUrl,
    JWT_SECRET: jwtSecret,
    GEMINI_API_KEY: process.env.GEMINI_API_KEY!,
    NEXT_PUBLIC_APP_URL: appUrl,
    NODE_ENV: (process.env.NODE_ENV as EnvConfig['NODE_ENV']) || 'development'
  };
}

/**
 * Get environment configuration (cached)
 */
let envConfig: EnvConfig | null = null;

export function getEnvConfig(): EnvConfig {
  if (!envConfig) {
    envConfig = validateEnv();
  }
  return envConfig;
}

/**
 * Check if running in production
 */
export function isProduction(): boolean {
  return getEnvConfig().NODE_ENV === 'production';
}

/**
 * Check if running in development
 */
export function isDevelopment(): boolean {
  return getEnvConfig().NODE_ENV === 'development';
}

/**
 * Get database URL with connection pooling settings
 */
export function getDatabaseUrl(): string {
  const config = getEnvConfig();
  const url = new URL(config.DATABASE_URL);
  
  // Add connection pooling parameters for production
  if (isProduction()) {
    url.searchParams.set('connection_limit', '10');
    url.searchParams.set('pool_timeout', '20');
  }
  
  return url.toString();
}

/**
 * Validate environment on startup
 */
export function validateEnvironmentOnStartup(): void {
  try {
    validateEnv();
    console.log('✅ Environment variables validated successfully');
  } catch (error) {
    console.error('❌ Environment validation failed:', error);
    process.exit(1);
  }
}
