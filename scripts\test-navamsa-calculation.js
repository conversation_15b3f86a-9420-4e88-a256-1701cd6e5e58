const { PrismaClient } = require('@prisma/client');
const { calculateBirth<PERSON><PERSON>, calculateEnhancedBirthChart, calculateSriLankanHoroscope } = require('../src/lib/astrology');

async function testNavamsaCalculation() {
  console.log('🧪 SRI LANKAN HOROSCOPE VALIDATION TEST');
  console.log('=' .repeat(80));
  console.log('Testing two real horoscope cases to verify calculations');
  console.log('=' .repeat(80));

  // Test Case 1
  await testSingleCase({
    name: 'Test Case 1',
    birthDate: new Date('2024-10-16'),
    birthTime: '09:18',
    birthPlace: 'Peradeniya, Sri Lanka',
    latitude: 7.2667,
    longitude: 80.5913,
    expectedLagna: 'Scorpio',
    expectedNavamsa: 'Sagittarius',
    qrToken: 'test-case-1-2024'
  });

  console.log('\n' + '=' .repeat(80));

  // Test Case 2
  await testSingleCase({
    name: 'Test Case 2',
    birthDate: new Date('1996-11-05'),
    birthTime: '22:51', // 10:51 PM
    birthPlace: 'Matale, Sri Lanka',
    latitude: 7.4675,
    longitude: 80.6234,
    expectedLagna: 'Gemini',
    expectedNavamsa: 'Taurus',
    qrToken: 'test-case-2-1996'
  });
}

async function testSingleCase(testCase) {
  console.log(`\n📋 ${testCase.name.toUpperCase()}:`);
  console.log(`📅 Birthday: ${testCase.birthDate.toDateString()}`);
  console.log(`⏰ Birth Time: ${testCase.birthTime} (Sri Lanka Time)`);
  console.log(`📍 Birth place: ${testCase.birthPlace}`);
  console.log(`🌍 Coordinates: ${testCase.latitude}°N, ${testCase.longitude}°E`);
  console.log('');
  console.log('🎯 EXPECTED RESULTS FROM REAL SRI LANKAN HOROSCOPE:');
  console.log(`   ✅ Expected Lagna: ${testCase.expectedLagna}`);
  console.log(`   ✅ Expected Navamsa: ${testCase.expectedNavamsa}`);
  console.log('-' .repeat(60));

  try {
    const prisma = new PrismaClient();

    // Create or update test user with specific birth details
    const testUser = await prisma.user.upsert({
      where: { qrToken: testCase.qrToken },
      update: {
        name: testCase.name,
        birthDate: testCase.birthDate,
        birthTime: testCase.birthTime,
        birthPlace: testCase.birthPlace,
        birthLatitude: testCase.latitude,
        birthLongitude: testCase.longitude,
        zodiacSign: 'libra', // We'll verify this
        languagePreference: 'en'
      },
      create: {
        name: testCase.name,
        email: `${testCase.qrToken}@example.com`,
        birthDate: testCase.birthDate,
        birthTime: testCase.birthTime,
        birthPlace: testCase.birthPlace,
        birthLatitude: testCase.latitude,
        birthLongitude: testCase.longitude,
        zodiacSign: 'libra',
        languagePreference: 'en',
        qrToken: testCase.qrToken
      }
    });

    console.log('✅ Test user created/updated:', testUser.id);

    // Create QR code mapping if it doesn't exist
    await prisma.qrCodeMapping.upsert({
      where: { qrToken: testCase.qrToken },
      update: { userId: testUser.id },
      create: {
        qrToken: testCase.qrToken,
        userId: testUser.id,
        scanCount: 0
      }
    });

    // Calculate birth chart with the exact details
    const birthDateTime = new Date(`${testCase.birthDate.toISOString().split('T')[0]}T${testCase.birthTime}:00`);
    const timezone = 'Asia/Colombo';

    console.log('\n🔄 STEP 1: Testing Enhanced Birth Chart Calculation...');
    console.log('📊 Birth DateTime:', birthDateTime.toISOString());
    console.log('🕐 Timezone:', timezone);

    const birthDetails = {
      birthDate: birthDateTime,
      birthTime: testCase.birthTime,
      latitude: testUser.birthLatitude,
      longitude: testUser.birthLongitude,
      timezone: timezone
    };

    // Test enhanced birth chart calculation
    const enhancedChartData = await calculateEnhancedBirthChart(birthDetails);

    console.log('\n🔄 STEP 2: Testing Sri Lankan Horoscope Calculation...');
    let sriLankanData = null;
    try {
      sriLankanData = await calculateSriLankanHoroscope(birthDetails);
      console.log('✅ Sri Lankan horoscope calculation successful');
    } catch (error) {
      console.error('❌ Sri Lankan horoscope calculation failed:', error.message);
    }

    // Also test the basic chart for comparison
    const basicChartData = await calculateBirthChart(
      birthDateTime,
      testUser.birthLatitude,
      testUser.birthLongitude,
      timezone
    );

    console.log('\n📈 STEP 3: VALIDATION RESULTS ANALYSIS');
    console.log('=' .repeat(60));

    // Test results tracking
    let lagnaTestPassed = false;
    let navamsaTestPassed = false;

    // 1. Test Sri Lankan Horoscope Data (Most Accurate)
    if (sriLankanData) {
      console.log('\n🇱🇰 SRI LANKAN HOROSCOPE CALCULATION RESULTS:');
      console.log('=' .repeat(50));
      console.log('🌅 Lagna (Ascendant):', sriLankanData.lagna);
      console.log('📊 Navamsa Chart:', sriLankanData.navamsa_chart);

      // Validate Lagna
      if (sriLankanData.lagna && sriLankanData.lagna.rashi) {
        console.log(`\n🎯 LAGNA VALIDATION:`);
        console.log(`   Expected: ${testCase.expectedLagna}`);
        console.log(`   Calculated: ${sriLankanData.lagna.rashi}`);
        lagnaTestPassed = sriLankanData.lagna.rashi === testCase.expectedLagna;
        console.log(`   Result: ${lagnaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
      }

      // Validate Navamsa - check first house for ascendant
      if (sriLankanData.navamsa_chart && sriLankanData.navamsa_chart.house1) {
        const navamsaAscendantPlanets = sriLankanData.navamsa_chart.house1;
        console.log(`\n🎯 NAVAMSA VALIDATION:`);
        console.log(`   Expected: ${testCase.expectedNavamsa}`);
        console.log(`   Navamsa House 1 planets: ${navamsaAscendantPlanets.join(', ') || 'None'}`);
        // For Navamsa, we need to check the ascendant's position, not just house 1
        console.log(`   Note: Need to check ascendant's navamsa position specifically`);
      }
    }

    // 2. Test Enhanced Chart Data
    console.log('\n🔮 ENHANCED CHART CALCULATION RESULTS:');
    console.log('=' .repeat(50));
    console.log('🌅 Ascendant (Lagna):', enhancedChartData.ascendant);
    console.log('🌙 Moon Sign:', enhancedChartData.moonSign);
    console.log('☀️ Sun Sign:', enhancedChartData.sunSign);

    // Validate Enhanced Chart Lagna
    if (enhancedChartData.ascendant) {
      console.log(`\n🎯 ENHANCED LAGNA VALIDATION:`);
      console.log(`   Expected: ${testCase.expectedLagna}`);
      console.log(`   Calculated: ${enhancedChartData.ascendant}`);
      const enhancedLagnaTest = enhancedChartData.ascendant === testCase.expectedLagna;
      console.log(`   Result: ${enhancedLagnaTest ? '✅ PASSED' : '❌ FAILED'}`);
      if (!lagnaTestPassed) lagnaTestPassed = enhancedLagnaTest;
    }

    console.log('\n📊 NAVAMSA CHART ANALYSIS:');
    console.log('=' .repeat(50));
    if (enhancedChartData.navamsaChart && enhancedChartData.navamsaChart.houses) {
      console.log('🏠 Navamsa Houses:');
      enhancedChartData.navamsaChart.houses.forEach((house, index) => {
        console.log(`   House ${index + 1}: ${house.sign} - Planets: ${house.planets.map(p => p.name).join(', ') || 'None'}`);
      });

      // Check the first house sign (Navamsa Ascendant)
      const navamsaAscendant = enhancedChartData.navamsaChart.houses[0].sign;
      console.log(`\n🎯 ENHANCED NAVAMSA VALIDATION:`);
      console.log(`   Expected: ${testCase.expectedNavamsa}`);
      console.log(`   Calculated: ${navamsaAscendant}`);
      navamsaTestPassed = navamsaAscendant === testCase.expectedNavamsa;
      console.log(`   Result: ${navamsaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
    }

    // 3. Test Basic Chart Data for comparison
    console.log('\n📊 BASIC CHART CALCULATION RESULTS:');
    console.log('=' .repeat(50));
    console.log('🌅 Ascendant (Lagna):', basicChartData.ascendant);
    console.log('🌙 Moon Sign:', basicChartData.moonSign);
    console.log('☀️ Sun Sign:', basicChartData.sunSign);

    // Final Test Summary
    console.log('\n🏆 FINAL TEST SUMMARY:');
    console.log('=' .repeat(60));
    console.log(`🎯 Lagna Test: ${lagnaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`🎯 Navamsa Test: ${navamsaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);

    const overallTestPassed = lagnaTestPassed && navamsaTestPassed;
    console.log(`\n🏆 OVERALL RESULT: ${overallTestPassed ? '✅ ALL TESTS PASSED' : '❌ TESTS FAILED'}`);

    if (!overallTestPassed) {
      console.log('\n🔧 DEBUGGING RECOMMENDATIONS:');
      if (!lagnaTestPassed) {
        console.log('   ❌ Lagna calculation needs fixing');
        console.log('   🔍 Check ascendant calculation algorithm');
        console.log('   🔍 Verify sidereal time calculation');
        console.log('   🔍 Check coordinate system and timezone handling');
      }
      if (!navamsaTestPassed) {
        console.log('   ❌ Navamsa calculation needs fixing');
        console.log('   🔍 Check Navamsa formula implementation');
        console.log('   🔍 Verify D-9 divisional chart logic');
        console.log('   🔍 Check ascendant navamsa position calculation');
      }
    }

    console.log('\n📊 ASHTAKAVARGA ANALYSIS:');
    console.log('=' .repeat(50));
    if (enhancedChartData.ashtakavarga && enhancedChartData.ashtakavarga.sarvashtakavarga) {
      console.log('🎯 Sarvashtakavarga totals:');
      enhancedChartData.ashtakavarga.sarvashtakavarga.forEach((total, index) => {
        console.log(`   House ${index + 1}: ${total} points`);
      });

      const totalPoints = enhancedChartData.ashtakavarga.sarvashtakavarga.reduce((sum, points) => sum + points, 0);
      console.log(`\n📊 Total Ashtakavarga Points: ${totalPoints}`);
      console.log('📝 Expected range: 290-340 points (traditional range)');

      if (totalPoints >= 290 && totalPoints <= 340) {
        console.log('✅ ASHTAKAVARGA CALCULATION LOOKS REASONABLE!');
      } else {
        console.log('⚠️ ASHTAKAVARGA TOTALS OUTSIDE EXPECTED RANGE');
      }
    }

    // Save the birth chart to database
    console.log('\n💾 Saving birth chart to database...');
    const birthChart = await prisma.birthChart.upsert({
      where: { userId: testUser.id },
      update: {
        birthDateTime: birthDateTime,
        birthPlace: testUser.birthPlace,
        birthLatitude: testUser.birthLatitude,
        birthLongitude: testUser.birthLongitude,
        timezone: timezone,
        planetPositions: enhancedChartData.planets,
        housePositions: enhancedChartData.houses,
        aspects: enhancedChartData.aspects,
        nakshatras: enhancedChartData.nakshatras,
        dashas: enhancedChartData.dashas,
        ascendant: enhancedChartData.ascendant,
        moonSign: enhancedChartData.moonSign,
        sunSign: enhancedChartData.sunSign,
        lagnaChart: enhancedChartData.lagnaChart,
        navamsaChart: enhancedChartData.navamsaChart,
        chandraChart: enhancedChartData.chandraChart,
        karakTable: enhancedChartData.karakTable,
        avasthaTable: enhancedChartData.avasthaTable,
        planetaryDetails: enhancedChartData.planetaryDetails,
        vimshottariDasha: enhancedChartData.vimshottariDasha,
        ashtakavarga: enhancedChartData.ashtakavarga,
        panchang: enhancedChartData.panchang,
        doshaAnalysis: enhancedChartData.doshaAnalysis,
        yogaAnalysis: enhancedChartData.yogaAnalysis,
        planetaryStrengths: enhancedChartData.planetaryStrengths,
        divisionalCharts: enhancedChartData.divisionalCharts,
        generalReading: enhancedChartData.generalReading,
        strengthsWeaknesses: enhancedChartData.strengthsWeaknesses,
        careerGuidance: enhancedChartData.careerGuidance,
        relationshipGuidance: enhancedChartData.relationshipGuidance,
        healthGuidance: enhancedChartData.healthGuidance,
        readingsEn: enhancedChartData.readingsEn,
        readingsSi: enhancedChartData.readingsSi,
        calculatedAt: new Date(),
        updatedAt: new Date()
      },
      create: {
        userId: testUser.id,
        birthDateTime: birthDateTime,
        birthPlace: testUser.birthPlace,
        birthLatitude: testUser.birthLatitude,
        birthLongitude: testUser.birthLongitude,
        timezone: timezone,
        planetPositions: enhancedChartData.planets,
        housePositions: enhancedChartData.houses,
        aspects: enhancedChartData.aspects,
        nakshatras: enhancedChartData.nakshatras,
        dashas: enhancedChartData.dashas,
        ascendant: enhancedChartData.ascendant,
        moonSign: enhancedChartData.moonSign,
        sunSign: enhancedChartData.sunSign,
        lagnaChart: enhancedChartData.lagnaChart,
        navamsaChart: enhancedChartData.navamsaChart,
        chandraChart: enhancedChartData.chandraChart,
        karakTable: enhancedChartData.karakTable,
        avasthaTable: enhancedChartData.avasthaTable,
        planetaryDetails: enhancedChartData.planetaryDetails,
        vimshottariDasha: enhancedChartData.vimshottariDasha,
        ashtakavarga: enhancedChartData.ashtakavarga,
        panchang: enhancedChartData.panchang,
        doshaAnalysis: enhancedChartData.doshaAnalysis,
        yogaAnalysis: enhancedChartData.yogaAnalysis,
        planetaryStrengths: enhancedChartData.planetaryStrengths,
        divisionalCharts: enhancedChartData.divisionalCharts,
        generalReading: enhancedChartData.generalReading,
        strengthsWeaknesses: enhancedChartData.strengthsWeaknesses,
        careerGuidance: enhancedChartData.careerGuidance,
        relationshipGuidance: enhancedChartData.relationshipGuidance,
        healthGuidance: enhancedChartData.healthGuidance,
        readingsEn: enhancedChartData.readingsEn,
        readingsSi: enhancedChartData.readingsSi,
        calculatedAt: new Date()
      }
    });

    console.log('✅ Birth chart saved with ID:', birthChart.id);

    console.log('\n🌐 TEST ACCESS:');
    console.log('=' .repeat(50));
    console.log(`🔗 QR Token: ${testCase.qrToken}`);
    console.log(`🌐 Test URL: http://localhost:3000/auth?token=${testCase.qrToken}`);
    console.log('📱 Navigate to Horoscope tab to view the birth charts');

    // Final test summary
    console.log('\n📋 FINAL TEST SUMMARY:');
    console.log('=' .repeat(50));
    console.log(`✅ Lagna Test: ${lagnaTestPassed ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Navamsa Test: ${navamsaTestPassed ? 'PASSED' : 'FAILED'}`);

    if (lagnaTestPassed && navamsaTestPassed) {
      console.log('\n🎉 ALL TESTS PASSED! Calculations are accurate.');
    } else {
      console.log('\n❌ SOME TESTS FAILED! Need to fix calculations.');
    }

    await prisma.$disconnect();

  } catch (error) {
    console.error(`❌ Error in ${testCase.name} test:`, error);
    throw error;
  }
}

// Run the test
testNavamsaCalculation()
  .then(() => {
    console.log('\n🎉 Navamsa calculation test completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Test failed:', error);
    process.exit(1);
  });
