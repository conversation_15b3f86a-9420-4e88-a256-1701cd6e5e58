// Check what's actually in the database for the test user
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUserData() {
  console.log('🔍 CHECKING USER DATA IN DATABASE');
  console.log('=' .repeat(60));

  try {
    const user = await prisma.user.findUnique({
      where: { id: 'cmd1xseqg0000pgn8783bx5ha' }
    });

    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('✅ User found:');
    console.log('📧 Email:', user.email);
    console.log('👤 Name:', user.name);
    console.log('📅 Birth Date:', user.birthDate?.toDateString());
    console.log('⏰ Birth Time:', user.birthTime);
    console.log('📍 Birth Place:', user.birthPlace);
    console.log('🌍 Latitude:', user.birthLatitude);
    console.log('🌍 Longitude:', user.birthLongitude);
    console.log('♈ Zodiac Sign:', user.zodiacSign);
    console.log('🕐 Created:', user.createdAt?.toISOString());
    console.log('🕐 Updated:', user.updatedAt?.toISOString());

    // Check if this matches our expected 1996 data
    const expected1996 = {
      birthDate: new Date('1996-11-05'),
      birthTime: '22:51',
      birthPlace: 'Matale, Sri Lanka',
      birthLatitude: 7.4675,
      birthLongitude: 80.6234
    };

    console.log('\n🎯 COMPARISON WITH EXPECTED 1996 DATA:');
    console.log('Birth Date Match:', user.birthDate?.toDateString() === expected1996.birthDate.toDateString() ? '✅' : '❌');
    console.log('Birth Time Match:', user.birthTime === expected1996.birthTime ? '✅' : '❌');
    console.log('Birth Place Match:', user.birthPlace === expected1996.birthPlace ? '✅' : '❌');
    console.log('Latitude Match:', user.birthLatitude === expected1996.birthLatitude ? '✅' : '❌');
    console.log('Longitude Match:', user.birthLongitude === expected1996.birthLongitude ? '✅' : '❌');

    if (user.birthDate?.toDateString() !== expected1996.birthDate.toDateString()) {
      console.log('\n🔧 UPDATING USER DATA TO 1996 CASE...');
      
      const updatedUser = await prisma.user.update({
        where: { id: 'cmd1xseqg0000pgn8783bx5ha' },
        data: {
          birthDate: expected1996.birthDate,
          birthTime: expected1996.birthTime,
          birthPlace: expected1996.birthPlace,
          birthLatitude: expected1996.birthLatitude,
          birthLongitude: expected1996.birthLongitude
        }
      });

      console.log('✅ User data updated successfully');
      console.log('📅 New Birth Date:', updatedUser.birthDate?.toDateString());
      console.log('⏰ New Birth Time:', updatedUser.birthTime);
      console.log('📍 New Birth Place:', updatedUser.birthPlace);
      console.log('🌍 New Coordinates:', updatedUser.birthLatitude, updatedUser.birthLongitude);
    } else {
      console.log('\n✅ User data already matches 1996 case');
    }

  } catch (error) {
    console.error('❌ Error checking user data:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkUserData();
