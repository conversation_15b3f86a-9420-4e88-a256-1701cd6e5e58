// Test API call to check actual calculations
const https = require('https');
const http = require('http');

async function testAPICall() {
  console.log('🧪 TESTING API CALL FOR HOROSCOPE CALCULATION');
  console.log('=' .repeat(60));

  try {
    // Test the birth chart API endpoint
    const postData = JSON.stringify({
      userId: 'cmd1xseqg0000pgn8783bx5ha' // Use the existing test user
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/birth-chart',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const data = await new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let body = '';
        res.on('data', (chunk) => body += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(body));
          } catch (e) {
            reject(e);
          }
        });
      });

      req.on('error', reject);
      req.write(postData);
      req.end();
    });
    console.log('✅ Birth chart API call successful');

    const birthChart = data.data?.birthChart;
    if (!birthChart) {
      throw new Error('Birth chart data not found in response');
    }

    console.log('\n📊 BIRTH CHART RESULTS:');
    console.log('Lagna Chart Houses:');
    if (birthChart.lagnaChart?.houses) {
      birthChart.lagnaChart.houses.forEach((house, index) => {
        console.log(`  House ${index + 1} (${house.sign}):`, house.planets?.map(p => p.name).join(', ') || 'Empty');
      });
    }

    console.log('\nNavamsa Chart Houses:');
    if (birthChart.navamsaChart?.houses) {
      birthChart.navamsaChart.houses.forEach((house, index) => {
        console.log(`  House ${index + 1} (${house.sign}):`, house.planets?.map(p => p.name).join(', ') || 'Empty');
      });
    }

    // Look for ascendant information
    console.log('\n🔍 ASCENDANT INFORMATION:');
    if (birthChart.lagnaChart?.houses) {
      const ascendantHouse = birthChart.lagnaChart.houses[0]; // First house is ascendant
      console.log('Lagna (Ascendant) Sign:', ascendantHouse.sign);
      console.log('Expected Lagna: Scorpio');
      console.log('✅ Lagna Match:', ascendantHouse.sign === 'Scorpio' ? 'CORRECT' : 'INCORRECT');
    }

    // Check navamsa ascendant
    if (birthChart.navamsaChart?.houses) {
      const navamsaAscendantHouse = birthChart.navamsaChart.houses[0];
      console.log('Navamsa Ascendant Sign:', navamsaAscendantHouse.sign);
      console.log('Expected Navamsa: Sagittarius');
      console.log('✅ Navamsa Match:', navamsaAscendantHouse.sign === 'Sagittarius' ? 'CORRECT' : 'INCORRECT');
    }

  } catch (error) {
    console.error('❌ Error in API test:', error.message);
  }
}

testAPICall();
