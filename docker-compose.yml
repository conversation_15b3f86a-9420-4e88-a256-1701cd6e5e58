version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: astroconnect_prod
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: astroconnect_prod_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./database/sample_data.sql:/docker-entrypoint-initdb.d/02-sample_data.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d astroconnect_prod"]
      interval: 10s
      timeout: 5s
      retries: 5

  # AstroConnect Application
  astroconnect:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**************************************************************/astroconnect_prod?schema=public
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL:-https://onlinekubera.store}
      - JWT_SECRET=${JWT_SECRET}
      - NEXT_TELEMETRY_DISABLED=1
    env_file:
      - .env.production
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy for SSL termination
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - astroconnect
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local

networks:
  default:
    name: astroconnect-network
