import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { LanguageProvider } from "@/hooks/useLanguage";
import { DialogProvider } from "@/contexts/DialogContext";

// Initialize background services
import "@/lib/startup";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AstroConnect - Your Personal Horoscope & Daily Guide",
  description: "Discover your cosmic destiny with personalized astrology insights. Get daily horoscopes, lucky guidance, and instant QR access to your personalized dashboard.",
  keywords: "astrology, horoscope, zodiac, daily guide, personalized predictions",
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "AstroConnect"
  },
  icons: {
    icon: "/favicon.png",
    apple: "/favicon.png"
  }
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: '#7c3aed'
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <LanguageProvider>
          <DialogProvider>
            {children}
          </DialogProvider>
        </LanguageProvider>
      </body>
    </html>
  );
}
