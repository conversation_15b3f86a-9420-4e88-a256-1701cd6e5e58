// This file initializes background services when the app starts
import { dailyReadingsScheduler } from './scheduler';
import { validateEnvironmentOnStartup } from './env-validation';

let isInitialized = false;

export function initializeServices() {
  if (isInitialized) {
    console.log('🔄 Services already initialized');
    return;
  }

  console.log('🚀 Initializing AstroConnect services...');

  try {
    // Validate environment variables first
    validateEnvironmentOnStartup();

    // Initialize the daily readings scheduler
    // The scheduler will start automatically when imported
    console.log('📅 Daily readings scheduler initialized');

    isInitialized = true;
    console.log('✅ All services initialized successfully');
  } catch (error) {
    console.error('❌ Error initializing services:', error);
    // Don't exit in development to allow for easier debugging
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    }
  }
}

// Auto-initialize when this module is imported
if (typeof window === 'undefined') {
  // Only run on server side
  initializeServices();
}
