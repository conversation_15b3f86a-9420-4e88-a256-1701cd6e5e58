import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyAdminToken } from '@/lib/auth';
import { calculateEnhancedBirthChart, BirthDetails } from '@/lib/astrology';
import { generateHoroscopeInterpretations } from '@/lib/horoscope-interpretations';
import { generateBirthChartTranslations } from '@/lib/birth-chart-translations';

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error },
        { status: 401 }
      );
    }

    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    console.log('🔮 Admin generating birth chart for user:', userId);

    // Check if birth chart already exists
    const existingChart = await prisma.birthChart.findUnique({
      where: { userId: userId }
    });

    if (existingChart) {
      return NextResponse.json(
        { success: false, error: 'Birth chart already exists for this user. Use regenerate instead.' },
        { status: 400 }
      );
    }

    // Get user data
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user has required birth data
    if (!user.birthDate || !user.birthTime || !user.birthLatitude || !user.birthLongitude) {
      return NextResponse.json(
        { success: false, error: 'Incomplete birth data. Birth date, time, and location are required.' },
        { status: 400 }
      );
    }

    // Prepare birth details
    const birthDetails: BirthDetails = {
      birthDate: new Date(user.birthDate),
      birthTime: user.birthTime,
      birthPlace: user.birthPlace || 'Unknown',
      latitude: user.birthLatitude,
      longitude: user.birthLongitude
    };

    console.log('📊 Birth details:', birthDetails);

    // Calculate enhanced birth chart with Vedic calculations
    const chartData = await calculateEnhancedBirthChart(birthDetails);
    console.log('✅ Chart calculated, generating interpretations...');

    // Generate interpretations
    const interpretations = generateHoroscopeInterpretations(chartData);
    console.log('✅ Interpretations generated');

    // Generate translations for the interpretations
    console.log('🌐 Generating translations for birth chart readings...');
    const translations = await generateBirthChartTranslations(interpretations);
    console.log('✅ Translations generated');

    // Determine timezone (simplified - you might want to use a proper timezone library)
    const timezone = getTimezoneFromCoordinates(user.birthLatitude, user.birthLongitude);

    // Create new birth chart record
    const birthChart = await prisma.birthChart.create({
      data: {
        userId: userId,
        birthDateTime: new Date(`${user.birthDate.toISOString().split('T')[0]}T${user.birthTime}:00`),
        birthPlace: user.birthPlace || 'Unknown',
        birthLatitude: user.birthLatitude,
        birthLongitude: user.birthLongitude,
        timezone: timezone,
        planetPositions: chartData.planets as any,
        housePositions: chartData.houses as any,
        aspects: chartData.aspects as any,
        nakshatras: chartData.nakshatras as any,
        dashas: chartData.dashas as any,
        ascendant: chartData.ascendant,
        moonSign: chartData.moonSign,
        sunSign: chartData.sunSign,
        // Enhanced Vedic Chart Data
        lagnaChart: chartData.lagnaChart as any,
        navamsaChart: chartData.navamsaChart as any,
        chandraChart: chartData.chandraChart as any,
        karakTable: chartData.karakTable as any,
        avasthaTable: chartData.avasthaTable as any,
        planetaryDetails: chartData.planetaryDetails as any,
        vimshottariDasha: chartData.vimshottariDasha as any,
        ashtakavarga: chartData.ashtakavarga as any,
        // Additional Vedic Calculations
        panchang: chartData.panchang as any,
        doshaAnalysis: chartData.doshaAnalysis as any,
        yogaAnalysis: chartData.yogaAnalysis as any,
        planetaryStrengths: chartData.planetaryStrengths as any,
        divisionalCharts: chartData.divisionalCharts as any,
        generalReading: interpretations.generalReading,
        strengthsWeaknesses: interpretations.strengthsWeaknesses,
        careerGuidance: interpretations.careerGuidance,
        relationshipGuidance: interpretations.relationshipGuidance,
        healthGuidance: interpretations.healthGuidance,
        readingsEn: translations.en as any,
        readingsSi: translations.si as any
      }
    });

    console.log('✅ Birth chart generated and saved to database');

    return NextResponse.json({
      success: true,
      message: 'Birth chart generated successfully',
      data: {
        birthChart: {
          id: birthChart.id,
          ascendant: birthChart.ascendant,
          moonSign: birthChart.moonSign,
          sunSign: birthChart.sunSign,
          calculatedAt: birthChart.calculatedAt
        }
      }
    });

  } catch (error) {
    console.error('❌ Error generating birth chart:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to generate birth chart' 
      },
      { status: 500 }
    );
  }
}

/**
 * Simple timezone detection based on coordinates
 * In a real application, you'd use a proper timezone library
 */
function getTimezoneFromCoordinates(latitude: number, longitude: number): string {
  // Sri Lanka timezone
  if (latitude >= 5.9 && latitude <= 9.9 && longitude >= 79.6 && longitude <= 81.9) {
    return 'Asia/Colombo';
  }
  
  // Simple longitude-based timezone estimation
  const timezoneOffset = Math.round(longitude / 15);
  return `UTC${timezoneOffset >= 0 ? '+' : ''}${timezoneOffset}`;
}
