// Horoscope API Validation Test
// Tests the accuracy of our horoscope calculations against real Sri Lankan horoscope data
// Birthday: 10/16/2024, Birth Time: 09:18 AM, Birth place: Peradeniya, Sri Lanka
// Expected Lagna: <PERSON><PERSON><PERSON>, Expected Navamsa: Sagittarius

const { PrismaClient } = require('@prisma/client');

async function testHoroscopeAPI() {
  console.log('🧪 COMPREHENSIVE HOROSCOPE API VALIDATION TEST');
  console.log('=' .repeat(60));
  console.log('📅 Birthday: 10/16/2024');
  console.log('⏰ Birth Time: 09:18 AM (Sri Lanka Time)');
  console.log('📍 Birth place: Peradeniya, Sri Lanka');
  console.log('🌍 Coordinates: 7.2667°N, 80.5913°E');
  console.log('');
  console.log('🎯 EXPECTED RESULTS FROM REAL SRI LANKAN HOROSCOPE:');
  console.log('   ✅ Expected Lagna: Scorpio');
  console.log('   ✅ Expected Navamsa: Sagittarius');
  console.log('=' .repeat(60));

  try {
    const prisma = new PrismaClient();

    // Create or update test user with specific birth details
    const testUser = await prisma.user.upsert({
      where: { qrToken: 'horoscope-api-validation-2024' },
      update: {
        name: 'Horoscope API Validation Test User',
        birthDate: new Date('2024-10-16'),
        birthTime: '09:18',
        birthPlace: 'Peradeniya, Sri Lanka',
        birthLatitude: 7.2667,
        birthLongitude: 80.5913,
        zodiacSign: 'libra', // We'll verify this
        languagePreference: 'en'
      },
      create: {
        name: 'Horoscope API Validation Test User',
        email: '<EMAIL>',
        birthDate: new Date('2024-10-16'),
        birthTime: '09:18',
        birthPlace: 'Peradeniya, Sri Lanka',
        birthLatitude: 7.2667,
        birthLongitude: 80.5913,
        zodiacSign: 'libra',
        languagePreference: 'en',
        qrToken: 'horoscope-api-validation-2024'
      }
    });

    console.log('✅ Test user created/updated:', testUser.id);

    // Create QR code mapping if it doesn't exist
    await prisma.qrCodeMapping.upsert({
      where: { qrToken: 'horoscope-api-validation-2024' },
      update: { userId: testUser.id },
      create: {
        qrToken: 'horoscope-api-validation-2024',
        userId: testUser.id,
        scanCount: 0
      }
    });

    console.log('\n🔄 STEP 1: Testing Birth Chart API...');
    
    // Test the birth chart API endpoint
    const apiUrl = 'http://localhost:3000/api/birth-chart';
    
    try {
      const fetch = (await import('node-fetch')).default;
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: testUser.id
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Birth chart API call successful');

      // Test results tracking
      let lagnaTestPassed = false;
      let navamsaTestPassed = false;

      console.log('\n📈 STEP 2: VALIDATION RESULTS ANALYSIS');
      console.log('=' .repeat(60));

      // Check the birth chart data
      if (data.birthChart) {
        console.log('\n🔮 BIRTH CHART API RESULTS:');
        console.log('=' .repeat(50));
        console.log('🌅 Ascendant (Lagna):', data.birthChart.ascendant);
        console.log('🌙 Moon Sign:', data.birthChart.moonSign);
        console.log('☀️ Sun Sign:', data.birthChart.sunSign);

        // Validate Lagna
        if (data.birthChart.ascendant) {
          console.log(`\n🎯 LAGNA VALIDATION:`);
          console.log(`   Expected: Scorpio`);
          console.log(`   Calculated: ${data.birthChart.ascendant}`);
          lagnaTestPassed = data.birthChart.ascendant === 'Scorpio';
          console.log(`   Result: ${lagnaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
        }

        // Check Navamsa chart
        if (data.birthChart.navamsaChart && data.birthChart.navamsaChart.houses) {
          console.log('\n📊 NAVAMSA CHART ANALYSIS:');
          console.log('=' .repeat(50));
          console.log('🏠 Navamsa Houses:');
          data.birthChart.navamsaChart.houses.forEach((house, index) => {
            console.log(`   House ${index + 1}: ${house.sign} - Planets: ${house.planets ? house.planets.map(p => p.name).join(', ') : 'None'}`);
          });

          // Check the first house sign (Navamsa Ascendant)
          const navamsaAscendant = data.birthChart.navamsaChart.houses[0].sign;
          console.log(`\n🎯 NAVAMSA VALIDATION:`);
          console.log(`   Expected: Sagittarius`);
          console.log(`   Calculated: ${navamsaAscendant}`);
          navamsaTestPassed = navamsaAscendant === 'Sagittarius';
          console.log(`   Result: ${navamsaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
        }

        // Check if there's Sri Lankan horoscope data
        if (data.sriLankanData) {
          console.log('\n🇱🇰 SRI LANKAN HOROSCOPE DATA:');
          console.log('=' .repeat(50));
          console.log('🌅 Lagna:', data.sriLankanData.lagna);
          console.log('📊 Navamsa Chart:', data.sriLankanData.navamsa_chart);
          
          if (data.sriLankanData.lagna && data.sriLankanData.lagna.rashi) {
            console.log(`\n🎯 SRI LANKAN LAGNA VALIDATION:`);
            console.log(`   Expected: Scorpio`);
            console.log(`   Calculated: ${data.sriLankanData.lagna.rashi}`);
            const sriLankanLagnaTest = data.sriLankanData.lagna.rashi === 'Scorpio';
            console.log(`   Result: ${sriLankanLagnaTest ? '✅ PASSED' : '❌ FAILED'}`);
            if (!lagnaTestPassed) lagnaTestPassed = sriLankanLagnaTest;
          }
        }
      }

      // Final Test Summary
      console.log('\n🏆 FINAL TEST SUMMARY:');
      console.log('=' .repeat(60));
      console.log(`🎯 Lagna Test: ${lagnaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
      console.log(`🎯 Navamsa Test: ${navamsaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
      
      const overallTestPassed = lagnaTestPassed && navamsaTestPassed;
      console.log(`\n🏆 OVERALL RESULT: ${overallTestPassed ? '✅ ALL TESTS PASSED' : '❌ TESTS FAILED'}`);
      
      if (!overallTestPassed) {
        console.log('\n🔧 DEBUGGING RECOMMENDATIONS:');
        if (!lagnaTestPassed) {
          console.log('   ❌ Lagna calculation needs fixing');
          console.log('   🔍 Check ascendant calculation algorithm in src/lib/astrology.ts');
          console.log('   🔍 Verify sidereal time calculation');
          console.log('   🔍 Check coordinate system and timezone handling');
        }
        if (!navamsaTestPassed) {
          console.log('   ❌ Navamsa calculation needs fixing');
          console.log('   🔍 Check Navamsa formula implementation in src/lib/astrology.ts');
          console.log('   🔍 Verify D-9 divisional chart logic');
          console.log('   🔍 Check ascendant navamsa position calculation');
        }
      }

      console.log('\n🌐 TEST ACCESS:');
      console.log('=' .repeat(50));
      console.log('🔗 QR Token: horoscope-api-validation-2024');
      console.log('🌐 Test URL: http://localhost:3000/auth?token=horoscope-api-validation-2024');
      console.log('📱 Navigate to Horoscope tab to view the birth charts');

      await prisma.$disconnect();

      return {
        lagnaTestPassed,
        navamsaTestPassed,
        overallTestPassed,
        data
      };

    } catch (apiError) {
      console.error('❌ API call failed:', apiError.message);
      console.log('\n💡 Make sure the Next.js development server is running:');
      console.log('   npm run dev');
      console.log('   or');
      console.log('   yarn dev');
      throw apiError;
    }

  } catch (error) {
    console.error('❌ Error in horoscope API validation test:', error);
    throw error;
  }
}

// Run the test
if (require.main === module) {
  testHoroscopeAPI()
    .then((results) => {
      console.log('\n🎉 Horoscope API validation test completed!');
      if (!results.overallTestPassed) {
        console.log('💡 Consider running additional debugging tests to identify calculation issues');
        process.exit(1);
      } else {
        console.log('🎊 All calculations match the real horoscope data!');
        process.exit(0);
      }
    })
    .catch((error) => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testHoroscopeAPI };
