import { PrismaClient } from '@prisma/client';
import { getDatabaseUrl, isProduction } from './env-validation';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Create Prisma client with optimized configuration
export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  datasources: {
    db: {
      url: getDatabaseUrl()
    }
  },
  log: isProduction() ? ['error'] : ['query', 'info', 'warn', 'error'],
  errorFormat: 'pretty'
});

if (!isProduction()) globalForPrisma.prisma = prisma;

// Helper function to disconnect Prisma (useful for serverless)
export async function disconnectPrisma() {
  await prisma.$disconnect();
}

// Helper function to check database connection
export async function checkDatabaseConnection() {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  }
}
