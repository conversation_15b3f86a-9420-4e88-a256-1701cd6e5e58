'use client';

import React, { useState, useEffect } from 'react';
import { User } from '@/types';
import { Search, Star, User as UserIcon, Sparkles, RefreshCw, Calendar, MapPin, Clock, Eye, Plus } from 'lucide-react';
import { useConfirmDialog, useAlertDialog } from '@/contexts/DialogContext';

interface BirthChart {
  id: string;
  userId: string;
  birthDateTime: string;
  birthPlace: string;
  birthLatitude: number;
  birthLongitude: number;
  timezone: string;
  ascendant: string;
  moonSign: string;
  sunSign: string;
  generalReading: string;
  strengthsWeaknesses: string;
  careerGuidance: string;
  relationshipGuidance: string;
  healthGuidance: string;
  spiritualGuidance: string;
  readingsEn?: any;
  readingsSi?: any;
  calculatedAt: string;
  // Enhanced Vedic Chart Data
  lagnaChart?: any;
  navamsaChart?: any;
  user: {
    id: string;
    name: string;
    email: string;
    zodiacSign: string;
    birthDate: string;
    birthTime?: string;
    birthPlace?: string;
  };
}

const UnifiedHoroscopeManagement: React.FC = () => {
  const [birthCharts, setBirthCharts] = useState<BirthChart[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedChart, setSelectedChart] = useState<BirthChart | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [regenerating, setRegenerating] = useState<string | null>(null);
  const { confirmDelete, confirmAction } = useConfirmDialog();
  const { showSuccess, showError } = useAlertDialog();

  useEffect(() => {
    fetchBirthCharts();
    fetchUsers();
  }, []);

  const fetchBirthCharts = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/birth-charts', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      if (data.success) {
        setBirthCharts(data.data);
      }
    } catch (error) {
      console.error('Error fetching birth charts:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/users', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      if (data.success) {
        setUsers(data.data.users);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const handleGenerateBirthChart = async (userId: string, userName: string) => {
    const confirmed = await confirmAction(
      'Generate Birth Chart',
      `Generate birth chart for ${userName}? This will calculate their personalized horoscope based on birth details.`,
      'Generate'
    );

    if (!confirmed) return;

    try {
      setRegenerating(userId);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/birth-charts/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ userId })
      });

      const data = await response.json();

      if (data.success) {
        showSuccess('Success', 'Birth chart generated successfully!');
        fetchBirthCharts();
      } else {
        showError('Error', data.error || 'Failed to generate birth chart');
      }
    } catch (error) {
      console.error('Error generating birth chart:', error);
      showError('Error', 'Failed to generate birth chart');
    } finally {
      setRegenerating(null);
    }
  };

  const handleRegenerateBirthChart = async (userId: string, userName: string) => {
    const confirmed = await confirmAction(
      'Regenerate Birth Chart',
      `Regenerate birth chart for ${userName}? This will recalculate their horoscope with updated interpretations.`,
      'Regenerate'
    );

    if (!confirmed) return;

    try {
      setRegenerating(userId);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/birth-charts/regenerate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ userId })
      });

      const data = await response.json();

      if (data.success) {
        showSuccess('Success', 'Birth chart regenerated successfully!');
        fetchBirthCharts();
      } else {
        showError('Error', data.error || 'Failed to regenerate birth chart');
      }
    } catch (error) {
      console.error('Error regenerating birth chart:', error);
      showError('Error', 'Failed to regenerate birth chart');
    } finally {
      setRegenerating(null);
    }
  };

  const handleViewDetails = (chart: BirthChart) => {
    setSelectedChart(chart);
    setShowDetailsModal(true);
  };

  // Filter birth charts based on search term
  const filteredCharts = birthCharts.filter(chart =>
    chart.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chart.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chart.user.zodiacSign.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get users without birth charts
  const usersWithoutCharts = users.filter(user => 
    user.birthDate && user.birthTime && user.birthLatitude && user.birthLongitude &&
    !birthCharts.some(chart => chart.userId === user.id)
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-400"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center">
            <Sparkles className="mr-2 text-purple-400" size={24} />
            User Birth Charts (Handahana)
          </h2>
          <p className="text-gray-300 mt-1">
            View and manage automatically generated birth charts that users see in their dashboard
          </p>
        </div>
      </div>

      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
        <input
          type="text"
          placeholder="Search by name, email, or zodiac sign..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-sm border border-white/10 rounded-lg p-6">
          <div className="flex items-center">
            <Star className="text-purple-400 mr-3" size={24} />
            <div>
              <p className="text-gray-300 text-sm">Total Birth Charts</p>
              <p className="text-2xl font-bold text-white">{birthCharts.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-500/20 to-cyan-500/20 backdrop-blur-sm border border-white/10 rounded-lg p-6">
          <div className="flex items-center">
            <UserIcon className="text-blue-400 mr-3" size={24} />
            <div>
              <p className="text-gray-300 text-sm">Users with Charts</p>
              <p className="text-2xl font-bold text-white">{birthCharts.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur-sm border border-white/10 rounded-lg p-6">
          <div className="flex items-center">
            <Plus className="text-green-400 mr-3" size={24} />
            <div>
              <p className="text-gray-300 text-sm">Pending Generation</p>
              <p className="text-2xl font-bold text-white">{usersWithoutCharts.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Users without birth charts */}
      {usersWithoutCharts.length > 0 && (
        <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 backdrop-blur-sm border border-green-400/20 rounded-lg p-6">
          <h3 className="text-xl font-bold text-white mb-4 flex items-center">
            <Plus className="mr-2 text-green-400" size={20} />
            Users Ready for Birth Chart Generation ({usersWithoutCharts.length})
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {usersWithoutCharts.map(user => (
              <div key={user.id} className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-semibold text-white">{user.name}</p>
                    <p className="text-sm text-gray-300">{user.email}</p>
                    <p className="text-xs text-blue-300">{user.zodiacSign}</p>
                  </div>
                  <button
                    onClick={() => handleGenerateBirthChart(user.id, user.name)}
                    disabled={regenerating === user.id}
                    className="bg-green-500 hover:bg-green-600 disabled:bg-gray-500 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center"
                  >
                    {regenerating === user.id ? (
                      <RefreshCw className="animate-spin" size={16} />
                    ) : (
                      <Plus size={16} />
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Existing birth charts */}
      <div className="bg-gradient-to-br from-purple-500/10 to-blue-500/10 backdrop-blur-sm border border-purple-400/20 rounded-lg p-6">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
          <Star className="mr-2 text-purple-400" size={20} />
          Generated Birth Charts ({filteredCharts.length})
        </h3>

        {filteredCharts.length === 0 ? (
          <div className="text-center py-8">
            <Star className="mx-auto text-gray-400 mb-4" size={48} />
            <p className="text-gray-400 text-lg">No birth charts found</p>
            <p className="text-gray-500 text-sm">Birth charts will appear here once generated</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredCharts.map(chart => (
              <div key={chart.id} className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <p className="font-semibold text-white">{chart.user.name}</p>
                    <p className="text-sm text-gray-300">{chart.user.email}</p>
                    <p className="text-xs text-blue-300">{chart.user.zodiacSign}</p>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleViewDetails(chart)}
                      className="bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-lg transition-colors"
                      title="View Details"
                    >
                      <Eye size={16} />
                    </button>
                    <button
                      onClick={() => handleRegenerateBirthChart(chart.userId, chart.user.name)}
                      disabled={regenerating === chart.userId}
                      className="bg-orange-500 hover:bg-orange-600 disabled:bg-gray-500 text-white p-2 rounded-lg transition-colors"
                      title="Regenerate"
                    >
                      {regenerating === chart.userId ? (
                        <RefreshCw className="animate-spin" size={16} />
                      ) : (
                        <RefreshCw size={16} />
                      )}
                    </button>
                  </div>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center text-gray-300">
                    <Calendar className="mr-2" size={14} />
                    {new Date(chart.birthDateTime).toLocaleDateString()}
                  </div>
                  <div className="flex items-center text-gray-300">
                    <Clock className="mr-2" size={14} />
                    {new Date(chart.birthDateTime).toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: true
                    })}
                  </div>
                  <div className="flex items-center text-gray-300">
                    <MapPin className="mr-2" size={14} />
                    {chart.birthPlace}
                  </div>
                </div>

                <div className="mt-3 pt-3 border-t border-white/10">
                  <div className="grid grid-cols-3 gap-2 text-xs mb-2">
                    <div className="text-center">
                      <p className="text-gray-400">Lagna</p>
                      <p className="text-white font-semibold">{chart.ascendant}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-gray-400">Navamsa</p>
                      <p className="text-white font-semibold">
                        {chart.navamsaChart?.navamsaAscendant || 'N/A'}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-gray-400">Moon</p>
                      <p className="text-white font-semibold">{chart.moonSign}</p>
                    </div>
                  </div>
                  <div className="text-center">
                    <p className="text-gray-400 text-xs">Sun Sign</p>
                    <p className="text-white font-semibold text-xs">{chart.sunSign}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default UnifiedHoroscopeManagement;
