// Test Sri Lankan Horoscope Calculations
// Verify the time zone handling and calculations match expected results

const { PrismaClient } = require('@prisma/client');
const { 
  getSriLankanUTCOffset, 
  convertToUTC, 
  calculateJulianDay,
  calculateSriLankanHoroscope,
  calculate<PERSON><PERSON>msa,
  getLagna<PERSON><PERSON>
} = require('./src/lib/astrology.ts');

const prisma = new PrismaClient();

async function testSriLankanCalculations() {
  console.log('🧪 TESTING SRI LANKAN HOROSCOPE CALCULATIONS');
  console.log('=' .repeat(80));

  // Test Case 1: 10/16/2024, 09:18 AM, Peradeniya
  console.log('\n📋 TEST CASE 1:');
  console.log('Birthday: 10/16/2024');
  console.log('Birth Time: 09:18 AM (Sri Lanka Time)');
  console.log('Birth Place: Peradeniya, Sri Lanka');
  console.log('Expected Lagna: Scorpio');
  console.log('Expected Navamsa: Sagittarius');
  console.log('-' .repeat(50));

  const testCase1 = {
    birthDate: new Date('2024-10-16'),
    birthTime: '09:18',
    latitude: 7.2667,  // Peradeniya coordinates
    longitude: 80.5913,
    birthPlace: 'Peradeniya, Sri Lanka'
  };

  await testBirthChart(testCase1, 'Scorpio', 'Sagittarius');

  console.log('\n' + '=' .repeat(80));

  // Test Case 2: 11/05/1996, 10:51 PM, Matale
  console.log('\n📋 TEST CASE 2:');
  console.log('Birthday: 11/05/1996');
  console.log('Birth Time: 10:51 PM (Sri Lanka Time)');
  console.log('Birth Place: Matale, Sri Lanka');
  console.log('Expected Lagna: Gemini');
  console.log('Expected Navamsa: Taurus');
  console.log('-' .repeat(50));

  const testCase2 = {
    birthDate: new Date('1996-11-05'),
    birthTime: '22:51', // 10:51 PM in 24-hour format
    latitude: 7.4675,   // Matale coordinates
    longitude: 80.6234,
    birthPlace: 'Matale, Sri Lanka'
  };

  await testBirthChart(testCase2, 'Gemini', 'Taurus');

  await prisma.$disconnect();
}

async function testBirthChart(birthDetails, expectedLagna, expectedNavamsa) {
  try {
    console.log('\n🕰️ Step 1: Time Zone Analysis');
    const utcOffset = getSriLankanUTCOffset(birthDetails.birthDate);
    console.log(`UTC Offset for ${birthDetails.birthDate.toDateString()}: +${utcOffset.toString().padStart(4, '0')}`);
    
    // Verify correct historical offset
    const year = birthDetails.birthDate.getFullYear();
    const month = birthDetails.birthDate.getMonth() + 1;
    const day = birthDetails.birthDate.getDate();
    
    let expectedOffset;
    if (year < 1996 || (year === 1996 && month < 5) || (year === 1996 && month === 5 && day < 25)) {
      expectedOffset = 5.5; // +05:30
    } else if (year === 1996 && month >= 5 && day >= 25 && !(month === 10 && day >= 26)) {
      expectedOffset = 6.5; // +06:30
    } else if (year === 1996 && month === 10 && day >= 26) {
      expectedOffset = 6.0; // +06:00
    } else if (year >= 2006 || (year === 2006 && month >= 4 && day >= 15)) {
      expectedOffset = 5.5; // +05:30
    } else {
      expectedOffset = 6.0; // +06:00 (between Oct 26, 1996 and Apr 15, 2006)
    }
    
    console.log(`Expected UTC Offset: +${expectedOffset.toString().padStart(4, '0')}`);
    console.log(`✅ UTC Offset ${utcOffset === expectedOffset ? 'CORRECT' : 'INCORRECT'}`);

    console.log('\n🌍 Step 2: UTC Conversion');
    const utcDateTime = convertToUTC(birthDetails.birthDate, birthDetails.birthTime);
    console.log(`Local Time: ${birthDetails.birthDate.toDateString()} ${birthDetails.birthTime}`);
    console.log(`UTC Time: ${utcDateTime.toISOString()}`);

    console.log('\n📅 Step 3: Julian Day Calculation');
    const julianDay = calculateJulianDay(utcDateTime);
    console.log(`Julian Day: ${julianDay}`);

    console.log('\n🔮 Step 4: Full Horoscope Calculation');
    const horoscope = await calculateSriLankanHoroscope(birthDetails);
    
    console.log('\n📊 RESULTS:');
    console.log(`Calculated Lagna: ${horoscope.lagna.rashi}`);
    console.log(`Expected Lagna: ${expectedLagna}`);
    console.log(`✅ Lagna ${horoscope.lagna.rashi === expectedLagna ? 'CORRECT' : 'INCORRECT'}`);

    // Check Navamsa - need to find ascendant's navamsa position
    const ascendantNavamsa = calculateNavamsa(horoscope.lagna.degree);
    console.log(`Calculated Navamsa: ${ascendantNavamsa}`);
    console.log(`Expected Navamsa: ${expectedNavamsa}`);
    console.log(`✅ Navamsa ${ascendantNavamsa === expectedNavamsa ? 'CORRECT' : 'INCORRECT'}`);

    console.log('\n🔍 DETAILED ANALYSIS:');
    console.log(`Ascendant Degree: ${horoscope.lagna.degree.toFixed(4)}°`);
    console.log(`UTC Birth Time: ${horoscope.utc_birth}`);
    console.log(`Julian Day: ${horoscope.julianday}`);

    // Test results summary
    const lagnaCorrect = horoscope.lagna.rashi === expectedLagna;
    const navamsaCorrect = ascendantNavamsa === expectedNavamsa;
    
    if (lagnaCorrect && navamsaCorrect) {
      console.log('\n🎉 ✅ ALL TESTS PASSED! Calculations are accurate.');
    } else {
      console.log('\n❌ TESTS FAILED! Need to fix calculations:');
      if (!lagnaCorrect) console.log(`  - Lagna calculation needs adjustment`);
      if (!navamsaCorrect) console.log(`  - Navamsa calculation needs adjustment`);
    }

  } catch (error) {
    console.error('❌ Error in test:', error);
  }
}

// Run the tests
testSriLankanCalculations().catch(console.error);
