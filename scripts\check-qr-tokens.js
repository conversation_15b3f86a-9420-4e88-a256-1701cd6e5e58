// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkQRTokens() {
  try {
    console.log('🔍 Checking QR tokens in database...');

    // Get all users with their QR tokens
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        qrToken: true,
        qrCodeMappings: {
          select: {
            qrToken: true,
            scanCount: true,
            lastScanned: true
          }
        }
      }
    });

    console.log(`\n📊 Found ${users.length} users:`);
    
    users.forEach((user, index) => {
      console.log(`\n${index + 1}. User: ${user.name}`);
      console.log(`   Email: ${user.email || 'N/A'}`);
      console.log(`   User QR Token: ${user.qrToken}`);
      console.log(`   QR URL: ${process.env.NEXT_PUBLIC_APP_URL}/qr/${user.qrToken}`);
      
      if (user.qrCodeMappings.length > 0) {
        console.log(`   QR Code Mappings:`);
        user.qrCodeMappings.forEach((mapping, idx) => {
          console.log(`     ${idx + 1}. Token: ${mapping.qrToken}`);
          console.log(`        Scans: ${mapping.scanCount}`);
          console.log(`        Last Scan: ${mapping.lastScanned || 'Never'}`);
        });
      } else {
        console.log(`   No QR code mappings found`);
      }
    });

    // Check for user named "Supun"
    const supunUser = users.find(user => 
      user.name.toLowerCase().includes('supun')
    );

    if (supunUser) {
      console.log(`\n🎯 Found user "Supun":`);
      console.log(`   Name: ${supunUser.name}`);
      console.log(`   QR Token: ${supunUser.qrToken}`);
      console.log(`   QR URL: ${process.env.NEXT_PUBLIC_APP_URL}/qr/${supunUser.qrToken}`);
      
      // Generate QR code for this user
      const QRCode = require('qrcode');
      const qrUrl = `${process.env.NEXT_PUBLIC_APP_URL}/qr/${supunUser.qrToken}`;
      
      try {
        const qrCodeDataUrl = await QRCode.toDataURL(qrUrl, {
          width: 300,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        });
        
        console.log(`\n📱 QR Code generated for Supun:`);
        console.log(`   Data URL length: ${qrCodeDataUrl.length} characters`);
        console.log(`   Contains: ${qrUrl}`);
        
        // Save QR code to file for testing
        const fs = require('fs');
        const base64Data = qrCodeDataUrl.replace(/^data:image\/png;base64,/, '');
        fs.writeFileSync('supun-qr-test.png', base64Data, 'base64');
        console.log(`   ✅ QR code saved to: supun-qr-test.png`);
        
      } catch (qrError) {
        console.error(`   ❌ Failed to generate QR code:`, qrError);
      }
    } else {
      console.log(`\n❌ No user named "Supun" found in database`);
    }

  } catch (error) {
    console.error('❌ Error checking QR tokens:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkQRTokens();
