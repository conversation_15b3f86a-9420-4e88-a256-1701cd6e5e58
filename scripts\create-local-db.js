#!/usr/bin/env node

/**
 * Create Local Database
 * This script creates the local PostgreSQL database for development
 */

const { Client } = require('pg');

async function createDatabase() {
  console.log('🚀 Creating local database for AstroConnect...\n');
  
  // Connect to PostgreSQL without specifying a database (connects to default 'postgres' db)
  const client = new Client({
    host: 'localhost',
    port: 5432,
    user: 'postgres',
    password: 'postgres', // Update this if your PostgreSQL has a different password
    database: 'postgres' // Connect to default database first
  });
  
  try {
    console.log('1️⃣ Connecting to PostgreSQL...');
    await client.connect();
    console.log('✅ Connected to PostgreSQL');
    
    console.log('\n2️⃣ Checking if database exists...');
    const result = await client.query(
      "SELECT 1 FROM pg_database WHERE datname = 'astroconnect_dev'"
    );
    
    if (result.rows.length > 0) {
      console.log('✅ Database "astroconnect_dev" already exists');
    } else {
      console.log('📝 Creating database "astroconnect_dev"...');
      await client.query('CREATE DATABASE astroconnect_dev');
      console.log('✅ Database "astroconnect_dev" created successfully');
    }
    
    console.log('\n🎉 Database setup completed!');
    console.log('\n📋 Next steps:');
    console.log('   1. Run: npx prisma db push');
    console.log('   2. Run: node scripts/populate-sample-data.js (optional)');
    console.log('   3. Run: npm run dev');
    
  } catch (error) {
    console.error('❌ Database creation failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    
    if (error.message.includes('authentication failed')) {
      console.log('   - Update the password in this script to match your PostgreSQL setup');
      console.log('   - Common passwords: postgres, password, or empty string');
      console.log('   - Check your PostgreSQL installation');
    } else if (error.message.includes('connection refused')) {
      console.log('   - Make sure PostgreSQL service is running');
      console.log('   - Check if PostgreSQL is installed');
    } else {
      console.log('   - Check PostgreSQL logs for more details');
      console.log('   - Verify PostgreSQL is properly configured');
    }
    
    console.log('\n🔧 Manual database creation:');
    console.log('   psql -U postgres');
    console.log('   CREATE DATABASE astroconnect_dev;');
    console.log('   \\q');
    
    process.exit(1);
  } finally {
    await client.end();
  }
}

createDatabase();
