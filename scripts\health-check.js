// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function runHealthCheck() {
  console.log('🏥 AstroConnect Health Check Starting...\n');
  
  let allTestsPassed = true;
  const results = [];

  // Test 1: Database Connection
  try {
    console.log('1️⃣ Testing database connection...');
    await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Database connection: PASSED');
    results.push({ test: 'Database Connection', status: 'PASSED' });
  } catch (error) {
    console.error('❌ Database connection: FAILED', error.message);
    results.push({ test: 'Database Connection', status: 'FAILED', error: error.message });
    allTestsPassed = false;
  }

  // Test 2: Environment Variables
  try {
    console.log('\n2️⃣ Testing environment variables...');
    const requiredVars = ['DATABASE_URL', 'JWT_SECRET', 'GEMINI_API_KEY', 'NEXT_PUBLIC_APP_URL'];
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      throw new Error(`Missing variables: ${missingVars.join(', ')}`);
    }
    
    console.log('✅ Environment variables: PASSED');
    results.push({ test: 'Environment Variables', status: 'PASSED' });
  } catch (error) {
    console.error('❌ Environment variables: FAILED', error.message);
    results.push({ test: 'Environment Variables', status: 'FAILED', error: error.message });
    allTestsPassed = false;
  }

  // Test 3: Admin Users
  try {
    console.log('\n3️⃣ Testing admin users...');
    const adminCount = await prisma.admin.count();
    const superAdminCount = await prisma.admin.count({ where: { role: 'super_admin' } });
    
    if (adminCount === 0) {
      throw new Error('No admin users found');
    }
    
    if (superAdminCount === 0) {
      throw new Error('No super admin found');
    }
    
    console.log(`✅ Admin users: PASSED (${adminCount} total, ${superAdminCount} super admin)`);
    results.push({ test: 'Admin Users', status: 'PASSED', details: `${adminCount} total, ${superAdminCount} super admin` });
  } catch (error) {
    console.error('❌ Admin users: FAILED', error.message);
    results.push({ test: 'Admin Users', status: 'FAILED', error: error.message });
    allTestsPassed = false;
  }

  // Test 4: System Settings
  try {
    console.log('\n4️⃣ Testing system settings...');
    const systemSettings = await prisma.systemSettings.findFirst();
    
    if (!systemSettings) {
      throw new Error('System settings not configured');
    }
    
    console.log(`✅ System settings: PASSED (Default language: ${systemSettings.defaultLanguage})`);
    results.push({ test: 'System Settings', status: 'PASSED', details: `Default language: ${systemSettings.defaultLanguage}` });
  } catch (error) {
    console.error('❌ System settings: FAILED', error.message);
    results.push({ test: 'System Settings', status: 'FAILED', error: error.message });
    allTestsPassed = false;
  }

  // Test 5: Database Schema Integrity
  try {
    console.log('\n5️⃣ Testing database schema integrity...');
    
    // Test all main tables exist and are accessible
    const tableTests = [
      { name: 'users', query: () => prisma.user.count() },
      { name: 'admins', query: () => prisma.admin.count() },
      { name: 'qr_code_mappings', query: () => prisma.qrCodeMapping.count() },
      { name: 'horoscopes', query: () => prisma.horoscope.count() },
      { name: 'daily_zodiac_readings', query: () => prisma.dailyZodiacReading.count() },
      { name: 'birth_charts', query: () => prisma.birthChart.count() },
      { name: 'system_settings', query: () => prisma.systemSettings.count() },
      { name: 'translation_cache', query: () => prisma.translationCache.count() }
    ];
    
    for (const table of tableTests) {
      await table.query();
    }
    
    console.log('✅ Database schema integrity: PASSED');
    results.push({ test: 'Database Schema Integrity', status: 'PASSED' });
  } catch (error) {
    console.error('❌ Database schema integrity: FAILED', error.message);
    results.push({ test: 'Database Schema Integrity', status: 'FAILED', error: error.message });
    allTestsPassed = false;
  }

  // Test 6: Password Hashing
  try {
    console.log('\n6️⃣ Testing password hashing...');
    const testPassword = 'test123';
    const hashedPassword = await bcrypt.hash(testPassword, 12);
    const isValid = await bcrypt.compare(testPassword, hashedPassword);
    
    if (!isValid) {
      throw new Error('Password hashing/verification failed');
    }
    
    console.log('✅ Password hashing: PASSED');
    results.push({ test: 'Password Hashing', status: 'PASSED' });
  } catch (error) {
    console.error('❌ Password hashing: FAILED', error.message);
    results.push({ test: 'Password Hashing', status: 'FAILED', error: error.message });
    allTestsPassed = false;
  }

  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('📋 HEALTH CHECK SUMMARY');
  console.log('='.repeat(50));
  
  results.forEach((result, index) => {
    const status = result.status === 'PASSED' ? '✅' : '❌';
    const details = result.details ? ` (${result.details})` : '';
    const error = result.error ? ` - ${result.error}` : '';
    console.log(`${index + 1}. ${result.test}: ${status} ${result.status}${details}${error}`);
  });
  
  console.log('\n' + '='.repeat(50));
  if (allTestsPassed) {
    console.log('🎉 ALL TESTS PASSED - Application is healthy!');
  } else {
    console.log('⚠️ SOME TESTS FAILED - Please check the issues above');
  }
  console.log('='.repeat(50));

  return allTestsPassed;
}

runHealthCheck()
  .then((success) => {
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('❌ Health check failed:', error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
