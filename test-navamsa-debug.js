// Test Navamsa calculation for the 1996 case
// Expected: Lagna=Gemini, Navamsa=Taurus

const VEDIC_SIGNS = [
  '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Aquarius', '<PERSON><PERSON><PERSON>'
];

function getSriLankanUTCOffset(birthDate) {
  const year = birthDate.getFullYear();
  const month = birthDate.getMonth() + 1;
  const day = birthDate.getDate();

  // Before 25 May 1996: +05:30
  if (year < 1996 || (year === 1996 && month < 5) || (year === 1996 && month === 5 && day < 25)) {
    return 5.5;
  }

  // 25 May 1996 → 26 Oct 1996: +06:30
  if (year === 1996 && month >= 5 && day >= 25 && !(month === 10 && day >= 26)) {
    return 6.5;
  }

  // 26 Oct 1996 → 15 Apr 2006: +06:00
  if (year < 2006 || (year === 2006 && month < 4) || (year === 2006 && month === 4 && day < 15)) {
    return 6.0;
  }

  // 15 Apr 2006 onward: +05:30
  return 5.5;
}

function convertToUTC(birthDate, birthTime) {
  const [hours, minutes] = birthTime.split(':').map(Number);
  const localDateTime = new Date(birthDate);
  localDateTime.setHours(hours, minutes, 0, 0);
  const utcOffset = getSriLankanUTCOffset(birthDate);
  const utcDateTime = new Date(localDateTime.getTime() - (utcOffset * 60 * 60 * 1000));
  return utcDateTime;
}

function calculateJulianDay(date) {
  const year = date.getUTCFullYear();
  const month = date.getUTCMonth() + 1;
  const day = date.getUTCDate();
  const hour = date.getUTCHours();
  const minute = date.getUTCMinutes();
  const second = date.getUTCSeconds();

  const a = Math.floor((14 - month) / 12);
  const y = year + 4800 - a;
  const m = month + 12 * a - 3;

  const jdn = day + Math.floor((153 * m + 2) / 5) + 365 * y + Math.floor(y / 4) - Math.floor(y / 100) + Math.floor(y / 400) - 32045;
  const jd = jdn + (hour - 12) / 24 + minute / 1440 + second / 86400;

  return jd;
}

function calculateLahiriAyanamsa(julianDay) {
  const t = (julianDay - 2451545.0) / 36525.0;
  const ayanamsa = 23.85 + 0.396 * t;
  return ayanamsa;
}

function calculateGreenwichSiderealTime(julianDay) {
  const t = (julianDay - 2451545.0) / 36525.0;
  const gst = 280.46061837 + 360.98564736629 * (julianDay - 2451545.0) + 0.000387933 * t * t - t * t * t / 38710000.0;
  return ((gst % 360) + 360) % 360;
}

function calculateLocalSiderealTime(julianDay, longitude) {
  const gst = calculateGreenwichSiderealTime(julianDay);
  const lst = gst + longitude / 15.0; // Convert longitude to time

  // Normalize to 0-24 hours
  const normalizedLst = ((lst / 15.0) % 24 + 24) % 24;

  console.log('🏠 Local Sidereal Time calculation:', {
    gst: gst.toFixed(6) + '°',
    longitude: longitude + '°',
    lst: lst.toFixed(6) + '°',
    normalizedLst: normalizedLst.toFixed(6) + 'h'
  });

  return normalizedLst;
}

function calculateAscendant(localSiderealTime, latitude) {
  // Convert LST from hours to degrees
  const lstDegrees = localSiderealTime * 15.0;

  // Convert to radians
  const latRad = latitude * Math.PI / 180.0;
  const lstRad = lstDegrees * Math.PI / 180.0;

  // Obliquity of ecliptic (approximately 23.44 degrees)
  const obliquity = 23.4392911 * Math.PI / 180.0;

  // Calculate ascendant using proper spherical trigonometry
  // Formula: tan(Asc) = cos(LST) / (sin(LST) * cos(obliquity) - tan(latitude) * sin(obliquity))
  const numerator = Math.cos(lstRad);
  const denominator = Math.sin(lstRad) * Math.cos(obliquity) - Math.tan(latRad) * Math.sin(obliquity);

  let ascendant = Math.atan2(numerator, denominator) * 180.0 / Math.PI;

  // Normalize to 0-360 degrees
  ascendant = ((ascendant % 360) + 360) % 360;

  console.log('🏠 Ascendant calculation debug:', {
    lstHours: localSiderealTime.toFixed(6) + 'h',
    lstDegrees: lstDegrees.toFixed(6) + '°',
    latitude: latitude + '°',
    obliquity: (obliquity * 180 / Math.PI).toFixed(6) + '°',
    ascendant: ascendant.toFixed(6) + '°'
  });

  return ascendant;
}

function calculateNavamsaPositionNew(longitude) {
  const normalizedLongitude = ((longitude % 360) + 360) % 360;
  const r = Math.floor(normalizedLongitude / 30);
  const d = normalizedLongitude % 30;
  const navamsaWidth = 10.0 / 3.0;
  const navamsaIndex = (r * 9 + Math.floor(d / navamsaWidth)) % 12;
  
  return {
    navamsaSign: navamsaIndex,
    navamsaLongitude: navamsaIndex * 30 + (d % navamsaWidth) * 9
  };
}

// Test the 2024 case first (should work)
console.log('🧪 TESTING NAVAMSA CALCULATION FOR 2024-10-16 CASE');
console.log('=' .repeat(60));

const birthDate = new Date('2024-10-16');
const birthTime = '09:18';
const latitude = 7.2667;
const longitude = 80.5913;

console.log('📅 Birth Date:', birthDate.toDateString());
console.log('⏰ Birth Time:', birthTime, '(Sri Lanka Time)');
console.log('📍 Birth Place: Peradeniya, Sri Lanka');
console.log('🎯 Expected Lagna: Scorpio');
console.log('🎯 Expected Navamsa: Sagittarius');
console.log('-' .repeat(60));

// Time zone conversion
console.log('\n🕰️ STEP 1: Time Zone Analysis');
const utcOffset = getSriLankanUTCOffset(birthDate);
console.log('UTC Offset for 2024-10-16: +' + utcOffset.toString().padStart(4, '0'));
console.log('Expected UTC Offset: ***** (15 Apr 2006 onward)');

// UTC conversion
const utcDateTime = convertToUTC(birthDate, birthTime);
console.log('\n🌍 STEP 2: UTC Conversion');
console.log('Local Time:', birthDate.toDateString(), birthTime);
console.log('UTC Time:', utcDateTime.toISOString());

// Julian Day
const julianDay = calculateJulianDay(utcDateTime);
console.log('\n📅 STEP 3: Julian Day:', julianDay);

// Ayanamsa
const ayanamsa = calculateLahiriAyanamsa(julianDay);
console.log('\n🌌 STEP 4: Lahiri Ayanamsa:', ayanamsa.toFixed(6) + '°');

// Ascendant
const localSiderealTime = calculateLocalSiderealTime(julianDay, longitude);
const tropicalAscendant = calculateAscendant(localSiderealTime, latitude);

// Apply ayanamsa correction to get sidereal ascendant
const siderealAscendant = ((tropicalAscendant - ayanamsa) % 360 + 360) % 360;

console.log('\n🏠 STEP 5: Ascendant Calculation');
console.log('Tropical Ascendant:', tropicalAscendant.toFixed(6) + '°');
console.log('Ayanamsa Correction:', ayanamsa.toFixed(6) + '°');
console.log('Sidereal Ascendant:', siderealAscendant.toFixed(6) + '°');

const lagnaSignIndex = Math.floor(siderealAscendant / 30);
const lagnaSign = VEDIC_SIGNS[lagnaSignIndex];
console.log('Lagna Sign:', lagnaSign, '(Expected: Scorpio)');
console.log('✅ Lagna', lagnaSign === 'Scorpio' ? 'CORRECT' : 'INCORRECT');

// Use sidereal ascendant for navamsa calculation
const ascendantDegree = siderealAscendant;

// Navamsa calculation
console.log('\n🕉️ STEP 6: Navamsa Calculation for Ascendant');
const navamsaResult = calculateNavamsaPositionNew(ascendantDegree);
const navamsaSign = VEDIC_SIGNS[navamsaResult.navamsaSign];
console.log('Ascendant Navamsa Sign:', navamsaSign, '(Expected: Sagittarius)');
console.log('✅ Navamsa', navamsaSign === 'Sagittarius' ? 'CORRECT' : 'INCORRECT');

// Debug navamsa calculation
console.log('\n🔍 NAVAMSA CALCULATION DEBUG:');
const r = Math.floor(ascendantDegree / 30);
const d = ascendantDegree % 30;
const navamsaWidth = 10.0 / 3.0;
const navamsaIndex = (r * 9 + Math.floor(d / navamsaWidth)) % 12;

console.log('Ascendant Longitude:', ascendantDegree.toFixed(6) + '°');
console.log('r (rashi number):', r, '(' + VEDIC_SIGNS[r] + ')');
console.log('d (degree within rashi):', d.toFixed(6) + '°');
console.log('navamsa width:', navamsaWidth.toFixed(6) + '°');
console.log('d / navamsa width:', (d / navamsaWidth).toFixed(6));
console.log('floor(d / navamsa width):', Math.floor(d / navamsaWidth));
console.log('(r * 9):', (r * 9));
console.log('(r * 9 + floor(d / navamsa width)):', (r * 9 + Math.floor(d / navamsaWidth)));
console.log('navamsa index:', navamsaIndex);
console.log('navamsa sign:', VEDIC_SIGNS[navamsaIndex]);

console.log('\n🎯 SUMMARY:');
console.log('Calculated Lagna:', lagnaSign, lagnaSign === 'Scorpio' ? '✅' : '❌');
console.log('Calculated Navamsa:', navamsaSign, navamsaSign === 'Sagittarius' ? '✅' : '❌');

if (lagnaSign !== 'Scorpio' || navamsaSign !== 'Sagittarius') {
  console.log('\n🔧 ISSUE IDENTIFIED:');
  console.log('The calculation is not matching the expected result.');
  console.log('Need to investigate the calculation implementation.');
}
