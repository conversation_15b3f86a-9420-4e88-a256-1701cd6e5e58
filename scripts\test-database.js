const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDatabase() {
  try {
    console.log('🔍 Testing database connection...');
    
    // Test basic connection
    await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Database connection successful');
    
    // Test table existence and count records
    const adminCount = await prisma.admin.count();
    const userCount = await prisma.user.count();
    const horoscopeCount = await prisma.horoscope.count();
    const dailyReadingCount = await prisma.dailyZodiacReading.count();
    
    console.log('\n📊 Database Statistics:');
    console.log(`- Admins: ${adminCount}`);
    console.log(`- Users: ${userCount}`);
    console.log(`- Horoscopes: ${horoscopeCount}`);
    console.log(`- Daily Readings: ${dailyReadingCount}`);
    
    // Test system settings
    const systemSettings = await prisma.systemSettings.findFirst();
    console.log(`- System Settings: ${systemSettings ? 'Configured' : 'Not configured'}`);
    
    // Test translation cache
    const translationCount = await prisma.translationCache.count();
    console.log(`- Translation Cache: ${translationCount} entries`);
    
    console.log('\n✅ All database tests passed!');
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

testDatabase();
