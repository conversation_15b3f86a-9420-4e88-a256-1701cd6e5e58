const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateTestUser() {
  console.log('🔧 Updating test user with birth time and place...');

  try {
    // Update <PERSON><PERSON> with birth time and place data
    const updatedUser = await prisma.user.update({
      where: { qrToken: 'cosmic789' },
      data: {
        birthTime: '14:30',
        birthPlace: 'Colombo, Sri Lanka',
        birthLatitude: 6.9271,
        birthLongitude: 79.8612
      }
    });

    console.log('✅ Updated user:', updatedUser.name);
    console.log('📍 Birth place:', updatedUser.birthPlace);
    console.log('⏰ Birth time:', updatedUser.birthTime);
    console.log('🌍 Coordinates:', updatedUser.birthLatitude, updatedUser.birthLongitude);

  } catch (error) {
    console.error('❌ Error updating user:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

updateTestUser()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
