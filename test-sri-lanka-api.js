const { PrismaClient } = require('@prisma/client');

async function testSriLankanCalculationsAPI() {
  console.log('🧪 TESTING SRI LANKAN HOROSCOPE CALCULATIONS VIA API');
  console.log('=' .repeat(80));

  const prisma = new PrismaClient();

  try {
    // Test Case 1: 10/16/2024, 09:18 AM, Peradeniya
    await testSingleCase(prisma, {
      name: 'Test Case 1',
      birthDate: new Date('2024-10-16'),
      birthTime: '09:18',
      birthPlace: 'Peradeniya, Sri Lanka',
      latitude: 7.2667,
      longitude: 80.5913,
      expectedLagna: 'Scorpio',
      expectedNavamsa: 'Sagittarius',
      qrToken: 'test-case-1-2024',
      email: '<EMAIL>'
    });

    console.log('\n' + '=' .repeat(80));

    // Test Case 2: 11/05/1996, 10:51 PM, <PERSON><PERSON>
    await testSingleCase(prisma, {
      name: 'Test Case 2',
      birthDate: new Date('1996-11-05'),
      birthTime: '22:51', // 10:51 PM
      birthPlace: 'Matale, Sri Lanka',
      latitude: 7.4675,
      longitude: 80.6234,
      expectedLagna: 'Gemini',
      expectedNavamsa: 'Taurus',
      qrToken: 'test-case-2-1996',
      email: '<EMAIL>'
    });

  } finally {
    await prisma.$disconnect();
  }
}

async function testSingleCase(prisma, testCase) {
  console.log(`\n📋 ${testCase.name.toUpperCase()}:`);
  console.log(`📅 Birthday: ${testCase.birthDate.toDateString()}`);
  console.log(`⏰ Birth Time: ${testCase.birthTime} (Sri Lanka Time)`);
  console.log(`📍 Birth place: ${testCase.birthPlace}`);
  console.log(`🌍 Coordinates: ${testCase.latitude}°N, ${testCase.longitude}°E`);
  console.log('');
  console.log('🎯 EXPECTED RESULTS FROM REAL SRI LANKAN HOROSCOPE:');
  console.log(`   ✅ Expected Lagna: ${testCase.expectedLagna}`);
  console.log(`   ✅ Expected Navamsa: ${testCase.expectedNavamsa}`);
  console.log('-' .repeat(60));

  try {
    // Create or update test user
    const testUser = await prisma.user.upsert({
      where: { qrToken: testCase.qrToken },
      update: {
        name: testCase.name,
        birthDate: testCase.birthDate,
        birthTime: testCase.birthTime,
        birthPlace: testCase.birthPlace,
        birthLatitude: testCase.latitude,
        birthLongitude: testCase.longitude,
        zodiacSign: 'libra',
        languagePreference: 'en'
      },
      create: {
        name: testCase.name,
        email: testCase.email,
        birthDate: testCase.birthDate,
        birthTime: testCase.birthTime,
        birthPlace: testCase.birthPlace,
        birthLatitude: testCase.latitude,
        birthLongitude: testCase.longitude,
        zodiacSign: 'libra',
        languagePreference: 'en',
        qrToken: testCase.qrToken
      }
    });

    console.log('✅ Test user created/updated:', testUser.id);

    // Create QR code mapping
    await prisma.qrCodeMapping.upsert({
      where: { qrToken: testCase.qrToken },
      update: { userId: testUser.id },
      create: {
        qrToken: testCase.qrToken,
        userId: testUser.id
      }
    });

    // Call the birth chart API
    console.log('\n🔄 Calling birth chart API...');
    
    const response = await fetch('http://localhost:3001/api/birth-chart', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: testUser.id
      })
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(`Birth chart calculation failed: ${result.error}`);
    }

    const birthChart = result.birthChart;
    
    console.log('\n📊 API RESPONSE ANALYSIS:');
    console.log('=' .repeat(50));
    console.log('🌅 Calculated Lagna:', birthChart.ascendant);
    console.log('🌙 Moon Sign:', birthChart.moonSign);
    console.log('☀️ Sun Sign:', birthChart.sunSign);

    // Check Lagna
    const lagnaCorrect = birthChart.ascendant === testCase.expectedLagna;
    console.log(`\n🎯 LAGNA VALIDATION:`);
    console.log(`   Expected: ${testCase.expectedLagna}`);
    console.log(`   Calculated: ${birthChart.ascendant}`);
    console.log(`   Result: ${lagnaCorrect ? '✅ PASSED' : '❌ FAILED'}`);

    // Check Navamsa
    let navamsaCorrect = false;
    if (birthChart.navamsaChart && birthChart.navamsaChart.navamsaAscendant) {
      const calculatedNavamsa = birthChart.navamsaChart.navamsaAscendant;
      navamsaCorrect = calculatedNavamsa === testCase.expectedNavamsa;
      console.log(`\n🎯 NAVAMSA VALIDATION:`);
      console.log(`   Expected: ${testCase.expectedNavamsa}`);
      console.log(`   Calculated: ${calculatedNavamsa}`);
      console.log(`   Result: ${navamsaCorrect ? '✅ PASSED' : '❌ FAILED'}`);
    } else {
      console.log(`\n🎯 NAVAMSA VALIDATION:`);
      console.log(`   Expected: ${testCase.expectedNavamsa}`);
      console.log(`   Calculated: Not available in response`);
      console.log(`   Result: ❌ FAILED (No navamsa data)`);
    }

    // Final summary
    console.log('\n📋 TEST SUMMARY:');
    console.log('=' .repeat(50));
    console.log(`✅ Lagna Test: ${lagnaCorrect ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Navamsa Test: ${navamsaCorrect ? 'PASSED' : 'FAILED'}`);
    
    if (lagnaCorrect && navamsaCorrect) {
      console.log('\n🎉 ALL TESTS PASSED! Calculations are accurate.');
    } else {
      console.log('\n❌ SOME TESTS FAILED! Need to fix calculations.');
      if (!lagnaCorrect) console.log('  - Lagna calculation needs adjustment');
      if (!navamsaCorrect) console.log('  - Navamsa calculation needs adjustment');
    }

    console.log(`\n🌐 Test URL: http://localhost:3001/auth?token=${testCase.qrToken}`);

  } catch (error) {
    console.error(`❌ Error in ${testCase.name}:`, error.message);
  }
}

// Run the test
testSriLankanCalculationsAPI()
  .then(() => {
    console.log('\n🎉 Sri Lankan calculations test completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Test failed:', error);
    process.exit(1);
  });
