// Horoscope Validation Test
// Tests the accuracy of our horoscope calculations against real Sri Lankan horoscope data
// Birthday: 10/16/2024, Birth Time: 09:18 AM, Birth place: Peradeniya, Sri Lanka
// Expected Lagna: <PERSON><PERSON><PERSON>, Expected Navamsa: Sagittarius

const { PrismaClient } = require('@prisma/client');

// Import the compiled JavaScript modules from the Next.js build
async function importAstrologyModule() {
  try {
    // Try to import from the built Next.js application
    const astrology = await import('./src/lib/astrology.ts');
    return astrology;
  } catch (error) {
    console.error('Failed to import astrology module:', error.message);
    console.log('Please ensure the Next.js application is built or run this test differently');
    throw error;
  }
}

async function testHoroscopeValidation() {
  console.log('🧪 COMPREHENSIVE HOROSCOPE VALIDATION TEST');
  console.log('=' .repeat(60));
  console.log('📅 Birthday: 10/16/2024');
  console.log('⏰ Birth Time: 09:18 AM (Sri Lanka Time)');
  console.log('📍 Birth place: Peradeniya, Sri Lanka');
  console.log('🌍 Coordinates: 7.2667°N, 80.5913°E');
  console.log('');
  console.log('🎯 EXPECTED RESULTS FROM REAL SRI LANKAN HOROSCOPE:');
  console.log('   ✅ Expected Lagna: Scorpio');
  console.log('   ✅ Expected Navamsa: Sagittarius');
  console.log('=' .repeat(60));

  try {
    const prisma = new PrismaClient();
    const astrology = await importAstrologyModule();

    // Create or update test user with specific birth details
    const testUser = await prisma.user.upsert({
      where: { qrToken: 'horoscope-validation-2024' },
      update: {
        name: 'Horoscope Validation Test User',
        birthDate: new Date('2024-10-16'),
        birthTime: '09:18',
        birthPlace: 'Peradeniya, Sri Lanka',
        birthLatitude: 7.2667,
        birthLongitude: 80.5913,
        zodiacSign: 'libra', // We'll verify this
        languagePreference: 'en'
      },
      create: {
        name: 'Horoscope Validation Test User',
        email: '<EMAIL>',
        birthDate: new Date('2024-10-16'),
        birthTime: '09:18',
        birthPlace: 'Peradeniya, Sri Lanka',
        birthLatitude: 7.2667,
        birthLongitude: 80.5913,
        zodiacSign: 'libra',
        languagePreference: 'en',
        qrToken: 'horoscope-validation-2024'
      }
    });

    console.log('✅ Test user created/updated:', testUser.id);

    // Create QR code mapping if it doesn't exist
    await prisma.qrCodeMapping.upsert({
      where: { qrToken: 'horoscope-validation-2024' },
      update: { userId: testUser.id },
      create: {
        qrToken: 'horoscope-validation-2024',
        userId: testUser.id,
        scanCount: 0
      }
    });

    // Calculate birth chart with the exact details
    const birthDateTime = new Date('2024-10-16T09:18:00');
    const timezone = 'Asia/Colombo';

    console.log('\n🔄 STEP 1: Testing Enhanced Birth Chart Calculation...');
    console.log('📊 Birth DateTime:', birthDateTime.toISOString());
    console.log('🕐 Timezone:', timezone);

    const birthDetails = {
      birthDate: birthDateTime,
      latitude: testUser.birthLatitude,
      longitude: testUser.birthLongitude,
      timezone: timezone
    };

    // Test results tracking
    let lagnaTestPassed = false;
    let navamsaTestPassed = false;
    let enhancedChartData = null;
    let sriLankanData = null;

    // Test enhanced birth chart calculation
    try {
      enhancedChartData = await astrology.calculateEnhancedBirthChart(birthDetails);
      console.log('✅ Enhanced birth chart calculation successful');
    } catch (error) {
      console.error('❌ Enhanced birth chart calculation failed:', error.message);
    }
    
    console.log('\n🔄 STEP 2: Testing Sri Lankan Horoscope Calculation...');
    try {
      sriLankanData = await astrology.calculateSriLankanHoroscope(birthDetails);
      console.log('✅ Sri Lankan horoscope calculation successful');
    } catch (error) {
      console.error('❌ Sri Lankan horoscope calculation failed:', error.message);
    }

    console.log('\n📈 STEP 3: VALIDATION RESULTS ANALYSIS');
    console.log('=' .repeat(60));
    
    // 1. Test Sri Lankan Horoscope Data (Most Accurate)
    if (sriLankanData) {
      console.log('\n🇱🇰 SRI LANKAN HOROSCOPE CALCULATION RESULTS:');
      console.log('=' .repeat(50));
      console.log('🌅 Lagna (Ascendant):', sriLankanData.lagna);
      console.log('📊 Navamsa Chart:', sriLankanData.navamsa_chart);
      
      // Validate Lagna
      if (sriLankanData.lagna && sriLankanData.lagna.rashi) {
        console.log(`\n🎯 LAGNA VALIDATION:`);
        console.log(`   Expected: Scorpio`);
        console.log(`   Calculated: ${sriLankanData.lagna.rashi}`);
        lagnaTestPassed = sriLankanData.lagna.rashi === 'Scorpio';
        console.log(`   Result: ${lagnaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
      }
      
      // Validate Navamsa - check first house for ascendant
      if (sriLankanData.navamsa_chart && sriLankanData.navamsa_chart.house1) {
        const navamsaAscendantPlanets = sriLankanData.navamsa_chart.house1;
        console.log(`\n🎯 NAVAMSA VALIDATION:`);
        console.log(`   Expected: Sagittarius`);
        console.log(`   Navamsa House 1 planets: ${navamsaAscendantPlanets.join(', ') || 'None'}`);
        console.log(`   Note: Need to check ascendant's navamsa position specifically`);
      }
    }
    
    // 2. Test Enhanced Chart Data
    if (enhancedChartData) {
      console.log('\n🔮 ENHANCED CHART CALCULATION RESULTS:');
      console.log('=' .repeat(50));
      console.log('🌅 Ascendant (Lagna):', enhancedChartData.ascendant);
      console.log('🌙 Moon Sign:', enhancedChartData.moonSign);
      console.log('☀️ Sun Sign:', enhancedChartData.sunSign);

      // Validate Enhanced Chart Lagna
      if (enhancedChartData.ascendant) {
        console.log(`\n🎯 ENHANCED LAGNA VALIDATION:`);
        console.log(`   Expected: Scorpio`);
        console.log(`   Calculated: ${enhancedChartData.ascendant}`);
        const enhancedLagnaTest = enhancedChartData.ascendant === 'Scorpio';
        console.log(`   Result: ${enhancedLagnaTest ? '✅ PASSED' : '❌ FAILED'}`);
        if (!lagnaTestPassed) lagnaTestPassed = enhancedLagnaTest;
      }

      console.log('\n📊 NAVAMSA CHART ANALYSIS:');
      console.log('=' .repeat(50));
      if (enhancedChartData.navamsaChart && enhancedChartData.navamsaChart.houses) {
        console.log('🏠 Navamsa Houses:');
        enhancedChartData.navamsaChart.houses.forEach((house, index) => {
          console.log(`   House ${index + 1}: ${house.sign} - Planets: ${house.planets.map(p => p.name).join(', ') || 'None'}`);
        });

        // Check the first house sign (Navamsa Ascendant)
        const navamsaAscendant = enhancedChartData.navamsaChart.houses[0].sign;
        console.log(`\n🎯 ENHANCED NAVAMSA VALIDATION:`);
        console.log(`   Expected: Sagittarius`);
        console.log(`   Calculated: ${navamsaAscendant}`);
        navamsaTestPassed = navamsaAscendant === 'Sagittarius';
        console.log(`   Result: ${navamsaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
      }
    }
    
    // Final Test Summary
    console.log('\n🏆 FINAL TEST SUMMARY:');
    console.log('=' .repeat(60));
    console.log(`🎯 Lagna Test: ${lagnaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`🎯 Navamsa Test: ${navamsaTestPassed ? '✅ PASSED' : '❌ FAILED'}`);
    
    const overallTestPassed = lagnaTestPassed && navamsaTestPassed;
    console.log(`\n🏆 OVERALL RESULT: ${overallTestPassed ? '✅ ALL TESTS PASSED' : '❌ TESTS FAILED'}`);
    
    if (!overallTestPassed) {
      console.log('\n🔧 DEBUGGING RECOMMENDATIONS:');
      if (!lagnaTestPassed) {
        console.log('   ❌ Lagna calculation needs fixing');
        console.log('   🔍 Check ascendant calculation algorithm');
        console.log('   🔍 Verify sidereal time calculation');
        console.log('   🔍 Check coordinate system and timezone handling');
      }
      if (!navamsaTestPassed) {
        console.log('   ❌ Navamsa calculation needs fixing');
        console.log('   🔍 Check Navamsa formula implementation');
        console.log('   🔍 Verify D-9 divisional chart logic');
        console.log('   🔍 Check ascendant navamsa position calculation');
      }
    }

    console.log('\n🌐 TEST ACCESS:');
    console.log('=' .repeat(50));
    console.log('🔗 QR Token: horoscope-validation-2024');
    console.log('🌐 Test URL: http://localhost:3000/auth?token=horoscope-validation-2024');
    console.log('📱 Navigate to Horoscope tab to view the birth charts');

    await prisma.$disconnect();

    // Return test results for further processing
    return {
      lagnaTestPassed,
      navamsaTestPassed,
      overallTestPassed,
      sriLankanData,
      enhancedChartData
    };

  } catch (error) {
    console.error('❌ Error in horoscope validation test:', error);
    throw error;
  }
}

// Run the test
if (require.main === module) {
  testHoroscopeValidation()
    .then((results) => {
      console.log('\n🎉 Horoscope validation test completed!');
      if (!results.overallTestPassed) {
        console.log('💡 Consider running additional debugging tests to identify calculation issues');
        process.exit(1);
      } else {
        console.log('🎊 All calculations match the real horoscope data!');
        process.exit(0);
      }
    })
    .catch((error) => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testHoroscopeValidation };
