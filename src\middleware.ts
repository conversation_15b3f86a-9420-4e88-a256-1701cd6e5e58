import { NextRequest, NextResponse } from 'next/server';

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Rate limiting configuration
const RATE_LIMITS = {
  '/api/auth/qr': { requests: 10, windowMs: 60000 }, // 10 requests per minute
  '/api/translate': { requests: 50, windowMs: 60000 }, // 50 requests per minute
  '/api/dashboard': { requests: 100, windowMs: 60000 }, // 100 requests per minute
  '/api/admin/users': { requests: 200, windowMs: 60000 }, // 200 requests per minute for user management
  '/api/admin/auth': { requests: 50, windowMs: 60000 }, // 50 requests per minute for admin auth
  '/api/admin': { requests: 100, windowMs: 60000 }, // 100 requests per minute for general admin operations
};

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  // In development, use a more specific identifier
  if (process.env.NODE_ENV === 'development') {
    return 'localhost-dev';
  }

  return 'unknown';
}

function isRateLimited(ip: string, endpoint: string): boolean {
  const config = RATE_LIMITS[endpoint as keyof typeof RATE_LIMITS];
  if (!config) return false;

  // In development, be more lenient with rate limiting
  if (process.env.NODE_ENV === 'development') {
    // Allow much higher limits in development
    const devConfig = { ...config, requests: config.requests * 10 };
    const key = `${ip}:${endpoint}`;
    const now = Date.now();
    const record = rateLimitStore.get(key);

    if (!record || now > record.resetTime) {
      rateLimitStore.set(key, {
        count: 1,
        resetTime: now + devConfig.windowMs
      });
      return false;
    }

    if (record.count >= devConfig.requests) {
      return true;
    }

    record.count++;
    return false;
  }

  const key = `${ip}:${endpoint}`;
  const now = Date.now();
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    // Reset or create new record
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + config.windowMs
    });
    return false;
  }

  if (record.count >= config.requests) {
    return true;
  }

  record.count++;
  return false;
}

// Clean up old entries periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, record] of rateLimitStore.entries()) {
    if (now > record.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}, 60000); // Clean up every minute

function validateInput(request: NextRequest): boolean {
  const contentType = request.headers.get('content-type');
  const method = request.method;

  // Only validate content-type for POST/PUT/PATCH requests with body
  if (method !== 'GET' && method !== 'HEAD' && contentType) {
    const allowedTypes = [
      'application/json',
      'multipart/form-data',
      'application/x-www-form-urlencoded',
      'text/plain'
    ];

    if (!allowedTypes.some(type => contentType.includes(type))) {
      return false;
    }
  }

  // Check for suspicious headers (only check for really dangerous ones)
  const suspiciousHeaders = ['x-original-url', 'x-rewrite-url'];
  for (const header of suspiciousHeaders) {
    if (request.headers.get(header)) {
      return false;
    }
  }

  return true;
}

function addSecurityHeaders(response: NextResponse): NextResponse {
  // Security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self' https://generativelanguage.googleapis.com",
    "media-src 'self'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; ');

  response.headers.set('Content-Security-Policy', csp);

  return response;
}

function addCacheHeaders(response: NextResponse, request: NextRequest): NextResponse {
  const url = request.nextUrl.pathname;

  // API routes - no cache for dynamic content
  if (url.startsWith('/api/')) {
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    response.headers.set('Surrogate-Control', 'no-store');
  }

  // Admin pages - no cache
  else if (url.startsWith('/admin')) {
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
  }

  // Dashboard and user pages - short cache with revalidation
  else if (url.startsWith('/dashboard') || url.startsWith('/auth')) {
    response.headers.set('Cache-Control', 'public, max-age=60, must-revalidate');
  }

  // Static assets - longer cache
  else if (url.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
  }

  // Default - short cache
  else {
    response.headers.set('Cache-Control', 'public, max-age=300, must-revalidate');
  }

  return response;
}

export function middleware(request: NextRequest) {
  const response = NextResponse.next();

  // Add security headers to all responses
  const secureResponse = addSecurityHeaders(response);

  // Add cache headers
  return addCacheHeaders(secureResponse, request);
}

export const config = {
  matcher: [
    '/api/:path*',
    '/((?!_next/static|_next/image|favicon.png).*)',
  ],
};
