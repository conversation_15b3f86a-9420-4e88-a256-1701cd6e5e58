// Test the Sri Lankan time conversion for the 1996 case
// Recreate the functions in JavaScript for testing

function getSriLankanUTCOffset(birthDate) {
  const year = birthDate.getFullYear();
  const month = birthDate.getMonth() + 1; // Convert to 1-based month
  const day = birthDate.getDate();

  // Before 25 May 1996: +05:30
  if (year < 1996 || (year === 1996 && month < 5) || (year === 1996 && month === 5 && day < 25)) {
    return 5.5; // +05:30
  }

  // 25 May 1996, 00:00 → 26 Oct 1996, 00:30: +06:30
  if (year === 1996 && month >= 5 && day >= 25 &&
      !(month === 10 && day >= 26)) {
    return 6.5; // +06:30
  }

  // 26 Oct 1996, 00:30 → 15 Apr 2006, 00:30: +06:00
  if ((year === 1996 && month === 10 && day >= 26) ||
      (year === 1996 && month > 10) ||  // Nov, Dec 1996
      (year > 1996 && year < 2006) ||
      (year === 2006 && month < 4) ||
      (year === 2006 && month === 4 && day < 15)) {
    return 6.0; // +06:00
  }

  // 15 Apr 2006, 00:30 onward: +05:30
  return 5.5; // +05:30
}

function convertToUTC(birthDate, birthTime) {
  const [hours, minutes] = birthTime.split(':').map(Number);

  // Get appropriate UTC offset for the birth date
  const utcOffset = getSriLankanUTCOffset(birthDate);

  // Create UTC datetime directly by treating input as Sri Lankan local time
  const year = birthDate.getFullYear();
  const month = birthDate.getMonth();
  const day = birthDate.getDate();

  // Calculate UTC hours and minutes
  const totalMinutes = hours * 60 + minutes;
  const utcTotalMinutes = totalMinutes - (utcOffset * 60);

  let utcHours = Math.floor(utcTotalMinutes / 60);
  let utcMinutes = utcTotalMinutes % 60;
  let utcDay = day;
  let utcMonth = month;
  let utcYear = year;

  // Handle day rollover
  if (utcHours < 0) {
    utcHours += 24;
    utcDay -= 1;
    if (utcDay < 1) {
      utcMonth -= 1;
      if (utcMonth < 0) {
        utcMonth = 11;
        utcYear -= 1;
      }
      // Get last day of previous month (simplified)
      utcDay = new Date(utcYear, utcMonth + 1, 0).getDate();
    }
  } else if (utcHours >= 24) {
    utcHours -= 24;
    utcDay += 1;
    const daysInMonth = new Date(utcYear, utcMonth + 1, 0).getDate();
    if (utcDay > daysInMonth) {
      utcDay = 1;
      utcMonth += 1;
      if (utcMonth > 11) {
        utcMonth = 0;
        utcYear += 1;
      }
    }
  }

  // Create UTC datetime
  const utcDateTime = new Date(Date.UTC(utcYear, utcMonth, utcDay, utcHours, utcMinutes, 0, 0));

  console.log('🕰️ Time conversion debug:', {
    inputDate: birthDate.toDateString(),
    inputTime: birthTime,
    sriLankanOffset: `+${utcOffset.toString().padStart(4, '0')}`,
    localTotalMinutes: totalMinutes,
    utcTotalMinutes: utcTotalMinutes,
    calculatedUTCHours: utcHours,
    calculatedUTCMinutes: utcMinutes,
    utcDateTime: utcDateTime.toISOString(),
    finalUTCHours: utcDateTime.getUTCHours(),
    finalUTCMinutes: utcDateTime.getUTCMinutes()
  });

  return utcDateTime;
}

console.log('🧪 TESTING SRI LANKAN TIME CONVERSION FOR 1996-11-05');
console.log('=' .repeat(60));

// Test case: 1996-11-05 22:51 (10:51 PM)
const birthDate = new Date('1996-11-05');
const birthTime = '22:51';

console.log('📅 Birth Date:', birthDate.toDateString());
console.log('⏰ Birth Time:', birthTime, '(Sri Lanka Local Time)');

// Get UTC offset
const utcOffset = getSriLankanUTCOffset(birthDate);
console.log('🌍 UTC Offset:', '+' + utcOffset.toString().padStart(4, '0'));

// Expected: +06:00 for 1996-11-05 (falls in 26 Oct 1996 → 15 Apr 2006 period)
console.log('🎯 Expected UTC Offset: +06:00');
console.log('✅ UTC Offset Match:', utcOffset === 6.0 ? 'CORRECT' : 'INCORRECT');

// Convert to UTC
const utcDateTime = convertToUTC(birthDate, birthTime);
console.log('\n🕰️ TIME CONVERSION:');
console.log('Local Time: 1996-11-05 22:51 (Sri Lanka)');
console.log('UTC Time:', utcDateTime.toISOString());

// Expected UTC time: 22:51 - 06:00 = 16:51 UTC
const expectedUTCHour = 16; // 22 - 6 = 16
const expectedUTCMinute = 51;

console.log('🎯 Expected UTC Time: 1996-11-05 16:51:00 UTC');
console.log('✅ UTC Hour Match:', utcDateTime.getUTCHours() === expectedUTCHour ? 'CORRECT' : 'INCORRECT');
console.log('✅ UTC Minute Match:', utcDateTime.getUTCMinutes() === expectedUTCMinute ? 'CORRECT' : 'INCORRECT');

console.log('\n📊 SUMMARY:');
console.log('UTC Offset:', utcOffset === 6.0 ? '✅ CORRECT' : '❌ INCORRECT');
console.log('UTC Time:', (utcDateTime.getUTCHours() === expectedUTCHour && utcDateTime.getUTCMinutes() === expectedUTCMinute) ? '✅ CORRECT' : '❌ INCORRECT');
