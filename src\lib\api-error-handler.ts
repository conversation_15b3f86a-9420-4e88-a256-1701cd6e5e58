import { NextResponse } from 'next/server';
import { ApiResponse } from '@/types';

export interface ErrorDetails {
  message: string;
  statusCode: number;
  context?: string;
}

/**
 * Standardized error handler for API routes
 */
export function handleApiError(error: unknown, context: string = 'API'): NextResponse<ApiResponse<null>> {
  console.error(`❌ ${context} Error:`, error);

  let errorMessage = 'Internal server error';
  let statusCode = 500;

  if (error instanceof Error) {
    errorMessage = error.message;
    
    // Log detailed error information
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      context
    });

    // Handle specific error types
    if (error.message.includes('Unique constraint')) {
      errorMessage = 'A record with this information already exists';
      statusCode = 409;
    } else if (error.message.includes('Invalid input')) {
      errorMessage = 'Invalid input data provided';
      statusCode = 400;
    } else if (error.message.includes('Record to update not found') || error.message.includes('Record not found')) {
      errorMessage = 'Record not found';
      statusCode = 404;
    } else if (error.message.includes('Unauthorized') || error.message.includes('Access denied')) {
      errorMessage = 'Unauthorized access';
      statusCode = 401;
    } else if (error.message.includes('Forbidden')) {
      errorMessage = 'Forbidden operation';
      statusCode = 403;
    } else if (error.message.includes('Validation failed')) {
      errorMessage = 'Validation failed';
      statusCode = 400;
    }
  }

  return NextResponse.json<ApiResponse<null>>({
    success: false,
    error: errorMessage
  }, { status: statusCode });
}

/**
 * Create a standardized success response
 */
export function createSuccessResponse<T>(
  data: T, 
  message: string = 'Operation completed successfully',
  statusCode: number = 200
): NextResponse<ApiResponse<T>> {
  return NextResponse.json<ApiResponse<T>>({
    success: true,
    data,
    message
  }, { status: statusCode });
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(
  error: string,
  statusCode: number = 400
): NextResponse<ApiResponse<null>> {
  return NextResponse.json<ApiResponse<null>>({
    success: false,
    error
  }, { status: statusCode });
}

/**
 * Validate required fields in request body
 */
export function validateRequiredFields(
  data: Record<string, any>,
  requiredFields: string[]
): string | null {
  const missingFields = requiredFields.filter(field => 
    data[field] === undefined || data[field] === null || data[field] === ''
  );

  if (missingFields.length > 0) {
    return `Missing required fields: ${missingFields.join(', ')}`;
  }

  return null;
}

/**
 * Sanitize error message for client response
 */
export function sanitizeErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    // Don't expose sensitive database errors to client
    if (error.message.includes('database') || error.message.includes('connection')) {
      return 'Database operation failed';
    }
    
    // Don't expose file system errors
    if (error.message.includes('ENOENT') || error.message.includes('file')) {
      return 'File operation failed';
    }
    
    return error.message;
  }
  
  return 'An unexpected error occurred';
}
