import { NextApiRequest, NextApiResponse } from 'next';
import { testNavamsaCalculation } from '../../../lib/astrology';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🧪 API: Starting Navamsa calculation test...');
    
    // Run the test function
    testNavamsaCalculation();
    
    console.log('✅ API: Navamsa calculation test completed');
    
    return res.status(200).json({ 
      success: true, 
      message: 'Navamsa calculation test completed. Check server logs for detailed results.',
      note: 'Expected: Real horoscope shows Sagittar<PERSON>, Our app shows Ari<PERSON> (investigating...)'
    });
    
  } catch (error) {
    console.error('❌ API: Error in Navamsa test:', error);
    return res.status(500).json({ 
      error: 'Failed to run Navamsa test',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
